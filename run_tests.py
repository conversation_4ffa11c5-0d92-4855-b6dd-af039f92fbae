#!/usr/bin/env python3
"""
Simple test runner for the well log prediction codebase
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'torch', 'numpy', 'matplotlib', 'h5py', 'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("📦 Please install them using:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required dependencies are installed")
    return True

def run_quick_test():
    """Run a quick import test"""
    print("🔍 Running quick import test...")
    
    try:
        # Test basic imports
        from core.utils import get_device
        from api.predictor import GeneralPredictor
        from configs.templates import get_available_templates
        
        print("✅ Basic imports successful")
        
        # Test device detection
        device = get_device()
        print(f"✅ Device detection: {device}")
        
        # Test templates
        templates = get_available_templates()
        print(f"✅ Available templates: {templates}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        return False

def run_full_test():
    """Run the complete workflow test"""
    print("🚀 Running complete workflow test...")
    
    try:
        # Import and run the test
        from test_complete_workflow import main
        return main() == 0
        
    except Exception as e:
        print(f"❌ Full test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🧪 Well Log Prediction Codebase Test Runner")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("core").exists() or not Path("api").exists():
        print("❌ Please run this script from the project root directory")
        print("   (The directory containing core/, api/, configs/, etc.)")
        return 1
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Run quick test first
    if not run_quick_test():
        print("\n❌ Quick test failed. Please fix import issues before running full test.")
        return 1
    
    print("\n" + "="*50)
    print("✅ Quick test passed! Running full workflow test...")
    print("="*50)
    
    # Run full test
    if run_full_test():
        print("\n🎉 All tests completed successfully!")
        print("📁 Check the 'test_outputs' directory for results")
        return 0
    else:
        print("\n❌ Full test failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
