"""
Template configurations for common prediction tasks
Minimal templates: 'vp_prediction' and 'density_prediction'
"""

TEMPLATES = {
    'vp_prediction': {
        'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
        'output_curves': ['VP'],
        'model_config': 'mwlt_base',
        'normalization_strategy': 'mixed',
        'training_config': {
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 100,
            'optimizer': 'adam'
        }
    },
    'density_prediction': {
        'input_curves': ['GR', 'CNL', 'VP', 'RLLD'],
        'output_curves': ['DEN'],
        'model_config': 'mwlt_base',
        'normalization_strategy': 'mixed',
        'training_config': {
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 100,
            'optimizer': 'adam'
        }
    }
}

def get_template(template_name):
    """
    Get template configuration
    
    Args:
        template_name: 'vp_prediction' or 'density_prediction'
        
    Returns:
        dict: Template configuration
    """
    template_name = template_name.lower()
    if template_name not in TEMPLATES:
        raise ValueError(f"Template '{template_name}' not found. Available: {list(TEMPLATES.keys())}")
    return TEMPLATES[template_name].copy()

def get_available_templates():
    """Get list of available template names"""
    return list(TEMPLATES.keys())