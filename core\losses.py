"""
Simple loss functions for single-target well log prediction
MSE with optional physics-range penalty
"""

import torch
import torch.nn as nn
from typing import Dict, Tu<PERSON>
from configs.curves import get_curve_definition

class SingleTargetLoss(nn.Module):
    """
    Simple MSE loss with optional physics constraint penalty
    Designed for single-target prediction of well log curves
    """
    
    def __init__(self, 
                 target_curve: str, 
                 use_physics_penalty: bool = True, 
                 penalty_weight: float = 0.1,
                 reduction: str = 'mean'):
        """
        Initialize SingleTargetLoss
        
        Args:
            target_curve: Name of target curve ('VP', 'DEN', 'GR', 'RLLD')
            use_physics_penalty: Whether to apply physics range penalty
            penalty_weight: Weight for physics penalty term
            reduction: 'mean' or 'sum' for loss reduction
        """
        super().__init__()
        self.target_curve = target_curve
        self.use_physics_penalty = use_physics_penalty
        self.penalty_weight = penalty_weight
        self.reduction = reduction
        
        # Base MSE loss
        self.mse_loss = nn.MSELoss(reduction=reduction)
        
        # Get physics range from curve definition
        try:
            curve_def = get_curve_definition(target_curve)
            self.physics_min, self.physics_max = curve_def['physics_range']
        except ValueError:
            print(f"Warning: No physics range found for {target_curve}. Disabling physics penalty.")
            self.physics_min, self.physics_max = 0.0, 1.0
            self.use_physics_penalty = False
        
        print(f"SingleTargetLoss initialized for '{target_curve}'")
        if self.use_physics_penalty:
            print(f"  Physics range: [{self.physics_min}, {self.physics_max}]")
            print(f"  Penalty weight: {penalty_weight}")
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Compute total loss = MSE + penalty_weight * physics_penalty
        
        Args:
            predictions: Model predictions [B, 1, L] or [B, L]
            targets: Target values [B, 1, L] or [B, L]
            
        Returns:
            torch.Tensor: Total loss (scalar)
        """
        # Ensure predictions and targets have compatible shapes
        if predictions.dim() == 2:
            predictions = predictions.unsqueeze(1)  # [B, 1, L]
        if targets.dim() == 2:
            targets = targets.unsqueeze(1)  # [B, 1, L]
        
        if predictions.shape != targets.shape:
            raise ValueError(f"Shape mismatch: predictions {predictions.shape} vs targets {targets.shape}")
        
        # Base MSE loss
        mse = self.mse_loss(predictions, targets)
        
        # Physics constraint penalty
        if self.use_physics_penalty:
            physics_penalty = self._compute_physics_penalty(predictions)
            total_loss = mse + self.penalty_weight * physics_penalty
        else:
            total_loss = mse
        
        return total_loss
    
    def _compute_physics_penalty(self, predictions: torch.Tensor) -> torch.Tensor:
        """
        Compute penalty for predictions outside physics range
        
        Args:
            predictions: Predictions [B, 1, L]
            
        Returns:
            torch.Tensor: Mean squared penalty for out-of-range values
        """
        # Extract prediction values
        pred_values = predictions.squeeze(1)  # [B, L]
        
        # Penalty for values below minimum
        below_min_penalty = torch.relu(self.physics_min - pred_values) ** 2
        
        # Penalty for values above maximum
        above_max_penalty = torch.relu(pred_values - self.physics_max) ** 2
        
        # Total penalty (mean over all elements)
        total_penalty = (below_min_penalty + above_max_penalty).mean()
        
        return total_penalty
    
    def get_loss_info(self) -> Dict:
        """
        Get information about the loss configuration
        
        Returns:
            dict: Loss configuration details
        """
        return {
            'target_curve': self.target_curve,
            'base_loss': 'MSE',
            'use_physics_penalty': self.use_physics_penalty,
            'penalty_weight': self.penalty_weight,
            'physics_range': (self.physics_min, self.physics_max),
            'reduction': self.reduction
        }

# Factory functions for common use cases
def create_vp_loss(penalty_weight: float = 0.1) -> SingleTargetLoss:
    """
    Create loss for VP (AC) prediction
    
    Args:
        penalty_weight: Weight for physics penalty
        
    Returns:
        SingleTargetLoss: Configured loss function
    """
    return SingleTargetLoss(
        target_curve='VP',
        use_physics_penalty=True,
        penalty_weight=penalty_weight
    )

def create_density_loss(penalty_weight: float = 0.05) -> SingleTargetLoss:
    """
    Create loss for density prediction
    
    Args:
        penalty_weight: Weight for physics penalty
        
    Returns:
        SingleTargetLoss: Configured loss function
    """
    return SingleTargetLoss(
        target_curve='DEN',
        use_physics_penalty=True,
        penalty_weight=penalty_weight
    )

def create_gamma_ray_loss(penalty_weight: float = 0.2) -> SingleTargetLoss:
    """
    Create loss for gamma ray prediction
    
    Args:
        penalty_weight: Weight for physics penalty
        
    Returns:
        SingleTargetLoss: Configured loss function
    """
    return SingleTargetLoss(
        target_curve='GR',
        use_physics_penalty=True,
        penalty_weight=penalty_weight
    )

def create_resistivity_loss(penalty_weight: float = 0.1) -> SingleTargetLoss:
    """
    Create loss for resistivity prediction
    
    Args:
        penalty_weight: Weight for physics penalty
        
    Returns:
        SingleTargetLoss: Configured loss function
    """
    return SingleTargetLoss(
        target_curve='RLLD',
        use_physics_penalty=True,
        penalty_weight=penalty_weight
    )

def create_curve_loss(curve_name: str, penalty_weight: float = 0.1) -> SingleTargetLoss:
    """
    Create loss for any curve type
    
    Args:
        curve_name: Target curve name
        penalty_weight: Weight for physics penalty
        
    Returns:
        SingleTargetLoss: Configured loss function
    """
    return SingleTargetLoss(
        target_curve=curve_name,
        use_physics_penalty=True,
        penalty_weight=penalty_weight
    )