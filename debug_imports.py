#!/usr/bin/env python3
"""
Debug script to check import issues
"""

import sys
import os
import traceback

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_individual_imports():
    """Test each import individually to identify issues"""
    
    imports_to_test = [
        ("core.utils", "get_device, calculate_rmse, calculate_r2, calculate_mae"),
        ("core.dataset", "SingleTargetDataset"),
        ("core.training", "TrainingManager"),
        ("core.model", "MWLT_Base"),
        ("api.predictor", "GeneralPredictor"),
        ("configs.templates", "get_available_templates, get_template"),
        ("configs.curves", "get_supported_curves"),
    ]
    
    print("🔍 Testing individual imports...")
    
    for module_name, items in imports_to_test:
        try:
            print(f"Testing: from {module_name} import {items}")
            exec(f"from {module_name} import {items}")
            print(f"✅ {module_name} - SUCCESS")
        except Exception as e:
            print(f"❌ {module_name} - FAILED: {e}")
            traceback.print_exc()
            print()

def check_file_structure():
    """Check if all required files exist"""
    
    required_files = [
        "core/__init__.py",
        "core/utils.py", 
        "core/dataset.py",
        "core/training.py",
        "core/model.py",
        "core/data_loader.py",
        "core/normalizer.py",
        "core/checkpoint.py",
        "core/losses.py",
        "api/__init__.py",
        "api/predictor.py",
        "configs/__init__.py",
        "configs/templates.py",
        "configs/curves.py",
        "configs/validation.py",
        "pipelines/__init__.py",
        "pipelines/train.py",
        "pipelines/infer.py"
    ]
    
    print("\n📁 Checking file structure...")
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ Missing files: {missing_files}")
        return False
    else:
        print("\n✅ All required files present")
        return True

def test_basic_functionality():
    """Test basic functionality"""
    
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test device detection
        from core.utils import get_device
        device = get_device()
        print(f"✅ Device detection: {device}")
        
        # Test templates
        from configs.templates import get_available_templates
        templates = get_available_templates()
        print(f"✅ Available templates: {templates}")
        
        # Test curve definitions
        from configs.curves import get_supported_curves
        curves = get_supported_curves()
        print(f"✅ Supported curves: {curves}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Import Debug Script")
    print("=" * 40)
    
    # Check current directory
    print(f"📁 Current directory: {os.getcwd()}")
    print(f"📁 Python path: {sys.path[:3]}...")  # Show first 3 entries
    
    # Check file structure
    structure_ok = check_file_structure()
    
    # Test imports
    test_individual_imports()
    
    # Test basic functionality
    if structure_ok:
        functionality_ok = test_basic_functionality()
        
        if functionality_ok:
            print("\n🎉 All basic tests passed! The main test should work.")
        else:
            print("\n❌ Basic functionality tests failed.")
    else:
        print("\n❌ File structure issues detected.")
