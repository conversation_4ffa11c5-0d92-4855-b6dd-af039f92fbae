#!/usr/bin/env python3
"""
Comprehensive Test Suite for Well Log Prediction Codebase
=========================================================

This script demonstrates the complete workflow including:
1. Synthetic data generation
2. Training pipeline testing (VP and Density prediction)
3. API-based prediction testing
4. Command-line inference testing
5. Visualization and metrics

Usage: python test_complete_workflow.py
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
import h5py
from pathlib import Path
from datetime import datetime
import subprocess
import traceback

# Add current directory to Python path for imports
sys.path.insert(0, os.getcwd())

# Import our modules
from core.utils import get_device, calculate_rmse, calculate_r2, calculate_mae
from core.dataset import SingleTargetDataset
from core.training import TrainingManager
from core.model import MWLT_Base
from api.predictor import GeneralPredictor
from configs.templates import get_available_templates, get_template
from configs.curves import get_supported_curves

class WellLogWorkflowTester:
    """Comprehensive tester for the well log prediction workflow"""
    
    def __init__(self, output_dir="test_outputs"):
        """Initialize the tester with output directory"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        self.data_dir = self.output_dir / "data"
        self.models_dir = self.output_dir / "models"
        self.plots_dir = self.output_dir / "plots"
        self.results_dir = self.output_dir / "results"
        
        for dir_path in [self.data_dir, self.models_dir, self.plots_dir, self.results_dir]:
            dir_path.mkdir(exist_ok=True)
        
        self.device = get_device()
        self.test_results = {}
        
        print(f"🚀 Well Log Workflow Tester Initialized")
        print(f"📁 Output directory: {self.output_dir.absolute()}")
        print(f"🖥️  Device: {self.device}")
        print("="*60)
    
    def generate_synthetic_data(self, num_samples=2000, sequence_length=720):
        """Generate synthetic well log data for testing"""
        print("📊 Generating synthetic well log data...")
        
        np.random.seed(42)  # For reproducible results
        
        # Generate depth array
        depth = np.linspace(1000, 1000 + sequence_length * 0.5, sequence_length)
        
        # Generate realistic well log curves with some correlation
        # Base trends
        depth_trend = (depth - depth.min()) / (depth.max() - depth.min())
        
        # Gamma Ray (API) - typically 20-150
        gr = 50 + 60 * np.sin(depth_trend * 4 * np.pi) + 20 * np.random.normal(0, 1, sequence_length)
        gr = np.clip(gr, 10, 200)
        
        # Neutron Porosity (CNL) - typically 0.05-0.45
        cnl = 0.2 + 0.15 * np.sin(depth_trend * 3 * np.pi + 1) + 0.05 * np.random.normal(0, 1, sequence_length)
        cnl = np.clip(cnl, 0.02, 0.5)
        
        # Bulk Density (g/cc) - typically 1.8-2.8, inversely correlated with porosity
        den = 2.65 - 0.8 * cnl + 0.1 * np.random.normal(0, 1, sequence_length)
        den = np.clip(den, 1.5, 3.0)
        
        # Resistivity (ohm.m) - log-normal distribution, typically 0.5-1000
        rlld = np.exp(2 + 2 * np.sin(depth_trend * 2 * np.pi) + 0.5 * np.random.normal(0, 1, sequence_length))
        rlld = np.clip(rlld, 0.1, 1000)
        
        # P-wave velocity (us/ft) - correlated with density and porosity
        # Wyllie time-average equation approximation
        vp = 189 / (cnl / 189 + (1 - cnl) / 55.5) + 10 * np.random.normal(0, 1, sequence_length)
        vp = np.clip(vp, 40, 200)
        
        # Create multiple samples by adding variations
        all_data = {}
        curves = ['DEPTH', 'GR', 'CNL', 'DEN', 'RLLD', 'VP']
        
        for curve in curves:
            all_data[curve] = []
        
        for i in range(num_samples):
            # Add sample-specific variations
            noise_factor = 0.1 + 0.05 * np.random.random()
            
            sample_gr = gr + noise_factor * 10 * np.random.normal(0, 1, sequence_length)
            sample_cnl = cnl + noise_factor * 0.02 * np.random.normal(0, 1, sequence_length)
            sample_den = den + noise_factor * 0.05 * np.random.normal(0, 1, sequence_length)
            sample_rlld = rlld * np.exp(noise_factor * 0.2 * np.random.normal(0, 1, sequence_length))
            sample_vp = vp + noise_factor * 5 * np.random.normal(0, 1, sequence_length)
            
            # Apply realistic constraints
            sample_gr = np.clip(sample_gr, 10, 200)
            sample_cnl = np.clip(sample_cnl, 0.02, 0.5)
            sample_den = np.clip(sample_den, 1.5, 3.0)
            sample_rlld = np.clip(sample_rlld, 0.1, 1000)
            sample_vp = np.clip(sample_vp, 40, 200)
            
            all_data['DEPTH'].append(depth + i * sequence_length * 0.5)
            all_data['GR'].append(sample_gr)
            all_data['CNL'].append(sample_cnl)
            all_data['DEN'].append(sample_den)
            all_data['RLLD'].append(sample_rlld)
            all_data['VP'].append(sample_vp)
        
        # Concatenate all samples
        for curve in curves:
            all_data[curve] = np.concatenate(all_data[curve])
        
        print(f"✅ Generated {len(all_data['DEPTH'])} data points")
        print(f"   Curves: {list(all_data.keys())}")
        
        return all_data
    
    def save_data_to_hdf5(self, data, filename):
        """Save data to HDF5 format"""
        filepath = self.data_dir / filename
        
        with h5py.File(filepath, 'w') as f:
            for curve_name, curve_data in data.items():
                f.create_dataset(curve_name, data=curve_data)
        
        print(f"💾 Saved data to {filepath}")
        return filepath
    
    def create_train_val_split(self, data, train_ratio=0.8):
        """Split data into training and validation sets"""
        total_length = len(data['DEPTH'])
        train_length = int(total_length * train_ratio)
        
        train_data = {}
        val_data = {}
        
        for curve_name, curve_data in data.items():
            train_data[curve_name] = curve_data[:train_length]
            val_data[curve_name] = curve_data[train_length:]
        
        return train_data, val_data

    def test_training_pipeline(self, template_name, train_file, val_file, epochs=5):
        """Test the training pipeline for a specific template"""
        print(f"\n🏋️ Testing Training Pipeline: {template_name}")
        print("-" * 50)

        try:
            # Create save directory for this template
            save_dir = self.models_dir / template_name
            save_dir.mkdir(exist_ok=True)

            # Create dataset
            print("📚 Creating datasets...")
            train_dataset = SingleTargetDataset(
                data_file=str(train_file),
                template_name=template_name,
                normalize=True,
                transform=True,
                device=self.device
            )

            val_dataset = SingleTargetDataset(
                data_file=str(val_file),
                template_name=template_name,
                normalize=True,
                transform=False,  # No augmentation for validation
                device=self.device
            )

            print(f"   Train dataset size: {len(train_dataset)}")
            print(f"   Val dataset size: {len(val_dataset)}")
            print(f"   Input curves: {train_dataset.input_curves}")
            print(f"   Target curve: {train_dataset.target_curve}")

            # Create model
            print("🧠 Creating model...")
            model = MWLT_Base(in_channels=len(train_dataset.input_curves), out_channels=1)
            model_params = sum(p.numel() for p in model.parameters())
            print(f"   Model parameters: {model_params:,}")

            # Create trainer
            print("🎯 Setting up trainer...")
            trainer = TrainingManager(
                model=model,
                train_dataset=train_dataset,
                val_dataset=val_dataset,
                target_curve=train_dataset.target_curve,
                learning_rate=0.001,
                batch_size=16,  # Smaller batch size for testing
                max_epochs=epochs,
                use_scheduler=True,
                grad_clip=1.0,
                save_path=str(save_dir)
            )

            # Train the model
            print("🚀 Starting training...")
            training_results = trainer.train()

            print(f"✅ Training completed!")
            print(f"   Best validation loss: {training_results['best_val_loss']:.6f}")
            print(f"   Final epoch: {training_results.get('final_epoch', epochs)}")

            # Store results
            self.test_results[f"{template_name}_training"] = {
                'success': True,
                'best_val_loss': training_results['best_val_loss'],
                'model_path': str(save_dir / 'best_model.pth'),
                'history_path': str(save_dir / 'training_history.json'),
                'final_epoch': training_results.get('final_epoch', epochs)
            }

            return True, str(save_dir / 'best_model.pth')

        except Exception as e:
            print(f"❌ Training failed: {str(e)}")
            traceback.print_exc()
            self.test_results[f"{template_name}_training"] = {
                'success': False,
                'error': str(e)
            }
            return False, None

    def test_api_prediction(self, template_name, model_path, test_data):
        """Test the GeneralPredictor API"""
        print(f"\n🔮 Testing API Prediction: {template_name}")
        print("-" * 50)

        try:
            # Initialize predictor
            print("🤖 Initializing GeneralPredictor...")
            predictor = GeneralPredictor.from_template(
                template_name=template_name,
                model_path=model_path,
                device_id=0 if 'cuda' in str(self.device) else -1
            )

            # Get template info
            template = get_template(template_name)
            input_curves = template['input_curves']
            output_curve = template['output_curves'][0]

            print(f"   Input curves: {input_curves}")
            print(f"   Output curve: {output_curve}")

            # Prepare test data (use a subset for prediction)
            test_length = 720  # Standard window size
            start_idx = len(test_data['DEPTH']) // 2  # Use middle section
            end_idx = start_idx + test_length

            input_data = {}
            for curve in input_curves:
                input_data[curve] = test_data[curve][start_idx:end_idx]

            # Get ground truth
            ground_truth = test_data[output_curve][start_idx:end_idx]

            print("🎯 Making predictions...")
            predictions = predictor.predict_from_curves(input_data)
            predicted_values = predictions[output_curve]

            # Calculate metrics
            rmse = calculate_rmse(predicted_values, ground_truth)
            r2 = calculate_r2(predicted_values, ground_truth)
            mae = calculate_mae(predicted_values, ground_truth)

            print(f"📊 Prediction Metrics:")
            print(f"   RMSE: {rmse:.4f}")
            print(f"   R²: {r2:.4f}")
            print(f"   MAE: {mae:.4f}")
            print(f"   Prediction range: {predicted_values.min():.2f} - {predicted_values.max():.2f}")
            print(f"   Ground truth range: {ground_truth.min():.2f} - {ground_truth.max():.2f}")

            # Store results
            self.test_results[f"{template_name}_api_prediction"] = {
                'success': True,
                'rmse': float(rmse),
                'r2': float(r2),
                'mae': float(mae),
                'prediction_range': [float(predicted_values.min()), float(predicted_values.max())],
                'ground_truth_range': [float(ground_truth.min()), float(ground_truth.max())]
            }

            return True, {
                'predictions': predicted_values,
                'ground_truth': ground_truth,
                'input_data': input_data,
                'metrics': {'rmse': rmse, 'r2': r2, 'mae': mae}
            }

        except Exception as e:
            print(f"❌ API prediction failed: {str(e)}")
            traceback.print_exc()
            self.test_results[f"{template_name}_api_prediction"] = {
                'success': False,
                'error': str(e)
            }
            return False, None

    def test_cli_inference(self, template_name, model_path, input_file):
        """Test the command-line inference pipeline"""
        print(f"\n💻 Testing CLI Inference: {template_name}")
        print("-" * 50)

        try:
            output_path = self.results_dir / f"{template_name}_cli_results"

            # Construct the command
            cmd = [
                sys.executable, "-m", "pipelines.infer",
                "--template", template_name,
                "--model_path", str(model_path),
                "--input_file", str(input_file),
                "--output_path", str(output_path),
                "--save_format", "json"
            ]

            print(f"🔧 Running command: {' '.join(cmd)}")

            # Run the command
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())

            if result.returncode == 0:
                print("✅ CLI inference completed successfully!")
                print("📄 Output:")
                print(result.stdout)

                # Check if output file exists
                output_file = output_path / "predictions.json"
                if output_file.exists():
                    with open(output_file, 'r') as f:
                        cli_results = json.load(f)

                    print(f"📊 CLI Results loaded:")
                    print(f"   Template: {cli_results.get('template', 'N/A')}")
                    print(f"   Input curves: {cli_results.get('input_curves', 'N/A')}")
                    print(f"   Output curves: {cli_results.get('output_curves', 'N/A')}")

                    self.test_results[f"{template_name}_cli_inference"] = {
                        'success': True,
                        'output_file': str(output_file),
                        'template': cli_results.get('template'),
                        'input_curves': cli_results.get('input_curves'),
                        'output_curves': cli_results.get('output_curves')
                    }

                    return True, cli_results
                else:
                    print("⚠️ Output file not found")
                    return False, None
            else:
                print(f"❌ CLI inference failed with return code {result.returncode}")
                print("Error output:")
                print(result.stderr)

                self.test_results[f"{template_name}_cli_inference"] = {
                    'success': False,
                    'error': result.stderr,
                    'return_code': result.returncode
                }
                return False, None

        except Exception as e:
            print(f"❌ CLI inference test failed: {str(e)}")
            traceback.print_exc()
            self.test_results[f"{template_name}_cli_inference"] = {
                'success': False,
                'error': str(e)
            }
            return False, None

    def create_training_plots(self, template_name):
        """Create training loss and metrics plots"""
        print(f"\n📈 Creating training plots for {template_name}...")

        try:
            # Load training history
            history_file = self.models_dir / template_name / "training_history.json"
            if not history_file.exists():
                print(f"⚠️ Training history file not found: {history_file}")
                return False

            with open(history_file, 'r') as f:
                history = json.load(f)

            # Create plots
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Training Results: {template_name.replace("_", " ").title()}', fontsize=16)

            epochs = range(1, len(history['train_loss']) + 1)

            # Training and validation loss
            axes[0, 0].plot(epochs, history['train_loss'], 'b-', label='Training Loss', linewidth=2)
            axes[0, 0].plot(epochs, history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].set_title('Training and Validation Loss')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # Learning rate (if available)
            if 'learning_rate' in history:
                axes[0, 1].plot(epochs, history['learning_rate'], 'g-', linewidth=2)
                axes[0, 1].set_xlabel('Epoch')
                axes[0, 1].set_ylabel('Learning Rate')
                axes[0, 1].set_title('Learning Rate Schedule')
                axes[0, 1].set_yscale('log')
                axes[0, 1].grid(True, alpha=0.3)
            else:
                axes[0, 1].text(0.5, 0.5, 'Learning Rate\nData Not Available',
                               ha='center', va='center', transform=axes[0, 1].transAxes)
                axes[0, 1].set_title('Learning Rate Schedule')

            # Training metrics (if available)
            if 'train_rmse' in history:
                axes[1, 0].plot(epochs, history['train_rmse'], 'b-', label='Training RMSE', linewidth=2)
                axes[1, 0].plot(epochs, history['val_rmse'], 'r-', label='Validation RMSE', linewidth=2)
                axes[1, 0].set_xlabel('Epoch')
                axes[1, 0].set_ylabel('RMSE')
                axes[1, 0].set_title('RMSE Over Time')
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)
            else:
                axes[1, 0].text(0.5, 0.5, 'RMSE Data\nNot Available',
                               ha='center', va='center', transform=axes[1, 0].transAxes)
                axes[1, 0].set_title('RMSE Over Time')

            # Loss improvement
            if len(history['val_loss']) > 1:
                loss_improvement = np.diff(history['val_loss'])
                axes[1, 1].plot(epochs[1:], loss_improvement, 'purple', linewidth=2)
                axes[1, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
                axes[1, 1].set_xlabel('Epoch')
                axes[1, 1].set_ylabel('Loss Change')
                axes[1, 1].set_title('Validation Loss Improvement')
                axes[1, 1].grid(True, alpha=0.3)
            else:
                axes[1, 1].text(0.5, 0.5, 'Insufficient Data\nfor Loss Improvement',
                               ha='center', va='center', transform=axes[1, 1].transAxes)
                axes[1, 1].set_title('Validation Loss Improvement')

            plt.tight_layout()

            # Save plot
            plot_file = self.plots_dir / f"{template_name}_training_plots.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ Training plots saved to {plot_file}")
            return True

        except Exception as e:
            print(f"❌ Failed to create training plots: {str(e)}")
            traceback.print_exc()
            return False

    def create_prediction_plots(self, template_name, prediction_data):
        """Create prediction comparison plots"""
        print(f"\n📊 Creating prediction plots for {template_name}...")

        try:
            predictions = prediction_data['predictions']
            ground_truth = prediction_data['ground_truth']
            input_data = prediction_data['input_data']
            metrics = prediction_data['metrics']

            # Create comprehensive plot
            fig, axes = plt.subplots(3, 2, figsize=(16, 12))
            fig.suptitle(f'Prediction Results: {template_name.replace("_", " ").title()}', fontsize=16)

            # Get template info
            template = get_template(template_name)
            output_curve = template['output_curves'][0]

            # Plot 1: Prediction vs Ground Truth
            axes[0, 0].plot(ground_truth, 'b-', label='Ground Truth', linewidth=2, alpha=0.8)
            axes[0, 0].plot(predictions, 'r-', label='Prediction', linewidth=2, alpha=0.8)
            axes[0, 0].set_xlabel('Sample Index')
            axes[0, 0].set_ylabel(f'{output_curve} Value')
            axes[0, 0].set_title(f'{output_curve} Prediction vs Ground Truth')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # Plot 2: Scatter plot
            axes[0, 1].scatter(ground_truth, predictions, alpha=0.6, s=20)
            min_val = min(ground_truth.min(), predictions.min())
            max_val = max(ground_truth.max(), predictions.max())
            axes[0, 1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, alpha=0.8)
            axes[0, 1].set_xlabel(f'Ground Truth {output_curve}')
            axes[0, 1].set_ylabel(f'Predicted {output_curve}')
            axes[0, 1].set_title(f'Prediction Scatter Plot (R² = {metrics["r2"]:.3f})')
            axes[0, 1].grid(True, alpha=0.3)

            # Plot 3: Residuals
            residuals = predictions - ground_truth
            axes[1, 0].plot(residuals, 'g-', linewidth=1.5, alpha=0.8)
            axes[1, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[1, 0].set_xlabel('Sample Index')
            axes[1, 0].set_ylabel('Residual')
            axes[1, 0].set_title(f'Prediction Residuals (RMSE = {metrics["rmse"]:.3f})')
            axes[1, 0].grid(True, alpha=0.3)

            # Plot 4: Residual histogram
            axes[1, 1].hist(residuals, bins=30, alpha=0.7, color='green', edgecolor='black')
            axes[1, 1].axvline(x=0, color='red', linestyle='--', linewidth=2)
            axes[1, 1].set_xlabel('Residual Value')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].set_title(f'Residual Distribution (MAE = {metrics["mae"]:.3f})')
            axes[1, 1].grid(True, alpha=0.3)

            # Plot 5: Input curves
            input_curves = list(input_data.keys())
            colors = ['blue', 'orange', 'green', 'red', 'purple', 'brown']

            for i, curve in enumerate(input_curves[:3]):  # Show first 3 input curves
                color = colors[i % len(colors)]
                axes[2, 0].plot(input_data[curve], color=color, label=curve, linewidth=1.5, alpha=0.8)

            axes[2, 0].set_xlabel('Sample Index')
            axes[2, 0].set_ylabel('Curve Value')
            axes[2, 0].set_title('Input Curves (First 3)')
            axes[2, 0].legend()
            axes[2, 0].grid(True, alpha=0.3)

            # Plot 6: Remaining input curves or metrics summary
            if len(input_curves) > 3:
                for i, curve in enumerate(input_curves[3:]):  # Show remaining curves
                    color = colors[(i + 3) % len(colors)]
                    axes[2, 1].plot(input_data[curve], color=color, label=curve, linewidth=1.5, alpha=0.8)

                axes[2, 1].set_xlabel('Sample Index')
                axes[2, 1].set_ylabel('Curve Value')
                axes[2, 1].set_title('Input Curves (Remaining)')
                axes[2, 1].legend()
                axes[2, 1].grid(True, alpha=0.3)
            else:
                # Show metrics summary
                metrics_text = f"""
Prediction Metrics Summary:

RMSE: {metrics['rmse']:.4f}
R²: {metrics['r2']:.4f}
MAE: {metrics['mae']:.4f}

Prediction Range:
{predictions.min():.2f} - {predictions.max():.2f}

Ground Truth Range:
{ground_truth.min():.2f} - {ground_truth.max():.2f}

Mean Absolute Error: {np.abs(residuals).mean():.4f}
Std of Residuals: {np.std(residuals):.4f}
                """
                axes[2, 1].text(0.1, 0.5, metrics_text, transform=axes[2, 1].transAxes,
                               fontsize=10, verticalalignment='center', fontfamily='monospace')
                axes[2, 1].set_title('Metrics Summary')
                axes[2, 1].axis('off')

            plt.tight_layout()

            # Save plot
            plot_file = self.plots_dir / f"{template_name}_prediction_plots.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ Prediction plots saved to {plot_file}")
            return True

        except Exception as e:
            print(f"❌ Failed to create prediction plots: {str(e)}")
            traceback.print_exc()
            return False

    def save_test_results(self):
        """Save comprehensive test results to JSON"""
        results_file = self.output_dir / "test_results_summary.json"

        # Add timestamp and summary
        summary = {
            'timestamp': datetime.now().isoformat(),
            'device': str(self.device),
            'output_directory': str(self.output_dir.absolute()),
            'test_results': self.test_results
        }

        # Calculate success rates
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        summary['success_rate'] = successful_tests / total_tests if total_tests > 0 else 0
        summary['total_tests'] = total_tests
        summary['successful_tests'] = successful_tests

        with open(results_file, 'w') as f:
            json.dump(summary, f, indent=2)

        print(f"\n📋 Test results saved to {results_file}")
        print(f"📊 Success rate: {successful_tests}/{total_tests} ({summary['success_rate']:.1%})")

        return results_file

    def run_complete_workflow(self, epochs=5):
        """Run the complete workflow test"""
        print("🚀 Starting Complete Well Log Prediction Workflow Test")
        print("=" * 70)

        start_time = datetime.now()

        try:
            # Step 1: Generate synthetic data
            print("\n📊 STEP 1: Data Generation")
            data = self.generate_synthetic_data(num_samples=500, sequence_length=720)

            # Split data
            train_data, val_data = self.create_train_val_split(data, train_ratio=0.8)

            # Save data files
            train_file = self.save_data_to_hdf5(train_data, "train_data.h5")
            val_file = self.save_data_to_hdf5(val_data, "val_data.h5")
            test_file = self.save_data_to_hdf5(val_data, "test_data.h5")  # Use val data for testing

            # Step 2: Test available templates
            templates = get_available_templates()
            print(f"\n🎯 Available templates: {templates}")

            for template_name in templates:
                print(f"\n{'='*70}")
                print(f"🧪 TESTING TEMPLATE: {template_name.upper()}")
                print(f"{'='*70}")

                # Step 3: Test training
                print(f"\n📚 STEP 3: Training Test")
                training_success, model_path = self.test_training_pipeline(
                    template_name, train_file, val_file, epochs=epochs
                )

                if training_success and model_path:
                    # Step 4: Create training plots
                    print(f"\n📈 STEP 4: Training Visualization")
                    self.create_training_plots(template_name)

                    # Step 5: Test API prediction
                    print(f"\n🔮 STEP 5: API Prediction Test")
                    api_success, prediction_data = self.test_api_prediction(
                        template_name, model_path, val_data
                    )

                    if api_success and prediction_data:
                        # Step 6: Create prediction plots
                        print(f"\n📊 STEP 6: Prediction Visualization")
                        self.create_prediction_plots(template_name, prediction_data)

                    # Step 7: Test CLI inference
                    print(f"\n💻 STEP 7: CLI Inference Test")
                    self.test_cli_inference(template_name, model_path, test_file)

                else:
                    print(f"⚠️ Skipping further tests for {template_name} due to training failure")

            # Step 8: Save results
            print(f"\n📋 STEP 8: Saving Results")
            self.save_test_results()

            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time

            print(f"\n🎉 WORKFLOW TEST COMPLETED!")
            print(f"⏱️  Total duration: {duration}")
            print(f"📁 All outputs saved to: {self.output_dir.absolute()}")

            # Print final summary
            self.print_final_summary()

            return True

        except Exception as e:
            print(f"\n❌ WORKFLOW TEST FAILED: {str(e)}")
            traceback.print_exc()
            return False

    def print_final_summary(self):
        """Print a comprehensive summary of all tests"""
        print(f"\n{'='*70}")
        print("📋 FINAL TEST SUMMARY")
        print(f"{'='*70}")

        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            print(f"{status} {test_name}")

            if result.get('success', False):
                # Print relevant metrics
                if 'best_val_loss' in result:
                    print(f"    📊 Best validation loss: {result['best_val_loss']:.6f}")
                if 'rmse' in result:
                    print(f"    📊 RMSE: {result['rmse']:.4f}, R²: {result['r2']:.4f}, MAE: {result['mae']:.4f}")
            else:
                if 'error' in result:
                    print(f"    ❌ Error: {result['error'][:100]}...")

        print(f"\n📁 Output files:")
        print(f"   📊 Data: {self.data_dir}")
        print(f"   🧠 Models: {self.models_dir}")
        print(f"   📈 Plots: {self.plots_dir}")
        print(f"   📋 Results: {self.results_dir}")


def main():
    """Main function to run the complete workflow test"""
    print("🔬 Well Log Prediction Codebase - Comprehensive Test Suite")
    print("=" * 70)

    # Initialize tester
    tester = WellLogWorkflowTester(output_dir="test_outputs")

    # Run the complete workflow
    success = tester.run_complete_workflow(epochs=3)  # Use fewer epochs for testing

    if success:
        print("\n🎉 All tests completed successfully!")
        return 0
    else:
        print("\n❌ Some tests failed. Check the output for details.")
        return 1


if __name__ == "__main__":
    exit(main())
