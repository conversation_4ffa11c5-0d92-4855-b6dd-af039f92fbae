# Well Log Prediction Codebase - Comprehensive Test Suite

This test suite provides a complete validation of the well log prediction codebase functionality after the recent folder move and import fixes.

## 📋 What the Test Suite Does

### 1. **Complete Workflow Testing**
- **Data Generation**: Creates realistic synthetic well log data (GR, CNL, DEN, RLLD, VP)
- **Training Pipeline**: Tests both VP and density prediction templates
- **API Testing**: Validates the `GeneralPredictor` API functionality
- **CLI Testing**: Tests command-line inference pipeline
- **Visualization**: Creates comprehensive plots and metrics

### 2. **Templates Tested**
- **VP Prediction**: `['GR', 'CNL', 'DEN', 'RLLD'] → ['VP']`
- **Density Prediction**: `['GR', 'CNL', 'VP', 'RLLD'] → ['DEN']`

### 3. **Outputs Generated**
```
test_outputs/
├── data/                          # Generated HDF5 data files
│   ├── train_data.h5
│   ├── val_data.h5
│   └── test_data.h5
├── models/                        # Trained model checkpoints
│   ├── vp_prediction/
│   │   ├── best_model.pth
│   │   └── training_history.json
│   └── density_prediction/
│       ├── best_model.pth
│       └── training_history.json
├── plots/                         # Visualization plots
│   ├── vp_prediction_training_plots.png
│   ├── vp_prediction_prediction_plots.png
│   ├── density_prediction_training_plots.png
│   └── density_prediction_prediction_plots.png
├── results/                       # CLI inference results
│   ├── vp_prediction_cli_results/
│   └── density_prediction_cli_results/
└── test_results_summary.json      # Complete test summary
```

## 🚀 How to Run the Tests

### Option 1: Quick Test Runner (Recommended)
```bash
python run_tests.py
```

This will:
1. Check dependencies
2. Run quick import tests
3. Execute the full workflow test
4. Provide a summary of results

### Option 2: Direct Test Execution
```bash
python test_complete_workflow.py
```

### Option 3: Individual Component Testing
```python
# Test specific components
from test_complete_workflow import WellLogWorkflowTester

tester = WellLogWorkflowTester()
data = tester.generate_synthetic_data()
# ... run specific tests
```

## 📊 Test Components Explained

### 1. **Synthetic Data Generation**
- Creates 500 samples of 720-point sequences
- Realistic well log curves with proper correlations
- Physics-based constraints and ranges
- Saves data in HDF5 format for testing

### 2. **Training Pipeline Test**
```python
# Tests core/training.py and pipelines/train.py
trainer = TrainingManager(
    model=MWLT_Base,
    train_dataset=SingleTargetDataset,
    val_dataset=SingleTargetDataset,
    # ... other parameters
)
results = trainer.train()
```

### 3. **API Prediction Test**
```python
# Tests api/predictor.py
predictor = GeneralPredictor.from_template(
    template_name='vp_prediction',
    model_path='./models/vp_prediction/best_model.pth'
)
predictions = predictor.predict_from_curves(input_data)
```

### 4. **CLI Inference Test**
```bash
# Tests pipelines/infer.py
python -m pipelines.infer \
    --template vp_prediction \
    --model_path ./models/vp_prediction/best_model.pth \
    --input_file ./data/test_data.h5 \
    --output_path ./results/vp_prediction_cli_results
```

### 5. **Visualization and Metrics**
- **Training Plots**: Loss curves, learning rate, RMSE over epochs
- **Prediction Plots**: Actual vs predicted, scatter plots, residuals
- **Metrics**: RMSE, R², MAE calculations using `core.utils`

## 📈 Expected Results

### Training Metrics
- **VP Prediction**: Typically achieves validation loss < 0.1 after a few epochs
- **Density Prediction**: Similar performance with appropriate convergence

### Prediction Metrics
- **RMSE**: Should be reasonable for the synthetic data scale
- **R²**: Should be > 0.8 for good predictions
- **MAE**: Mean absolute error within expected ranges

### Visual Outputs
- **Training curves**: Should show decreasing loss over epochs
- **Prediction plots**: Should show good correlation between actual and predicted
- **Residual plots**: Should be roughly centered around zero

## 🔧 Troubleshooting

### Common Issues

1. **Import Errors**
   ```
   ❌ ModuleNotFoundError: No module named 'core'
   ```
   **Solution**: Run from the project root directory containing `core/`, `api/`, etc.

2. **Missing Dependencies**
   ```
   ❌ Missing required packages: torch, matplotlib
   ```
   **Solution**: Install requirements: `pip install -r requirements.txt`

3. **CUDA Issues**
   ```
   ⚠️ CUDA not available, using CPU
   ```
   **Solution**: This is normal if no GPU is available. Tests will run on CPU.

4. **Memory Issues**
   ```
   ❌ RuntimeError: CUDA out of memory
   ```
   **Solution**: Reduce batch size or use CPU by setting `device_id=-1`

### Debug Mode
To run with more detailed output:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
python test_complete_workflow.py
```

## 📋 Test Results Interpretation

### Success Indicators
- ✅ All imports successful
- ✅ Training completes without errors
- ✅ Models save and load correctly
- ✅ Predictions generate reasonable outputs
- ✅ CLI inference works
- ✅ Plots are generated

### Failure Indicators
- ❌ Import errors (check file structure)
- ❌ Training fails (check data or model issues)
- ❌ Prediction errors (check model loading)
- ❌ CLI failures (check command-line interface)

### Performance Indicators
- **Training Speed**: Should complete 3-5 epochs in reasonable time
- **Memory Usage**: Should not exceed available system memory
- **Prediction Accuracy**: Metrics should be within expected ranges

## 🎯 What This Test Validates

1. **Import Structure**: All relative imports work correctly after folder move
2. **Data Pipeline**: HDF5 loading, normalization, windowing
3. **Model Architecture**: MWLT model creation and training
4. **Training Loop**: Complete training with checkpointing
5. **API Interface**: Programmatic prediction interface
6. **CLI Interface**: Command-line tools functionality
7. **Visualization**: Plotting and metrics calculation
8. **File I/O**: Model saving/loading, data persistence

This comprehensive test suite ensures that your well log prediction codebase is fully functional and ready for production use!
