"""
Simplified curve configurations for generalized well-log prediction
Minimal metadata: units, ranges, normalization params, aliases
"""

CURVE_DEFINITIONS = {
    'VP': {
        'aliases': ['AC', 'DTCO', 'DT'],
        'unit': 'us/ft',
        'physics_range': (40, 200),
        'normalization': 'standard',
        'interpolation_strategy': 'linear'
    },
    'DEN': {
        'aliases': ['RHOB', 'DENSITY'],
        'unit': 'g/cc',
        'physics_range': (1.5, 3.0),
        'normalization': 'standard',
        'interpolation_strategy': 'pchip'
    },
    'RLLD': {
        'aliases': ['RILD', 'RT', 'RESISTIVITY'],
        'unit': 'ohm.m',
        'physics_range': (0.1, 1000),
        'normalization': 'log_standard',
        'interpolation_strategy': 'log_domain_linear'
    },
    'GR': {
        'aliases': ['GAMMA', 'GR_TOTAL'],
        'unit': 'API',
        'physics_range': (0, 300),
        'normalization': 'standard',
        'interpolation_strategy': 'linear'
    }
}

def get_curve_definition(curve_name):
    """
    Get curve definition for a specific curve
    
    Args:
        curve_name: Name of the curve (e.g., 'VP', 'DEN', 'GR', 'RLLD')
        
    Returns:
        dict: Curve definition
    """
    curve_name = curve_name.upper()
    if curve_name not in CURVE_DEFINITIONS:
        raise ValueError(f"Curve '{curve_name}' not found. Available: {list(CURVE_DEFINITIONS.keys())}")
    return CURVE_DEFINITIONS[curve_name].copy()

def get_supported_curves():
    """Get list of all supported curve names"""
    return list(CURVE_DEFINITIONS.keys())