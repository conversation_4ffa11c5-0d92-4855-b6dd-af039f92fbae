# Generalized Well Log Prediction Architecture

## Overview
This module implements a configuration-driven ML stack for predicting well-log curves (Vp/AC, DEN, GR, RLLD) using the MWLT architecture.

## Quick Start
See examples in pipelines/train.py and api/predictor.py.

## Structure
- core/: Model, dataset, normalizer, etc.
- configs/: Curve definitions and templates
- api/: Prediction interface
- pipelines/: CLI tools
- tests/: Unit and integration tests