import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

try:
    print("Testing basic imports...")
    
    # Test core utils first
    print("Importing core.utils...")
    from core.utils import get_device
    print("✓ core.utils imported successfully")
    
    # Test device detection
    device = get_device()
    print(f"✓ Device detected: {device}")
    
    print("\nAll basic tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
