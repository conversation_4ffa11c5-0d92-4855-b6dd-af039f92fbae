"""
Check imports by examining the code structure
"""

import os
import sys

def check_file_exists(filepath):
    """Check if a file exists"""
    exists = os.path.exists(filepath)
    print(f"{'✓' if exists else '❌'} {filepath} {'exists' if exists else 'missing'}")
    return exists

def check_directory_structure():
    """Check if all required files exist"""
    print("Checking directory structure...")
    
    required_files = [
        '__init__.py',
        'core/__init__.py',
        'core/utils.py',
        'core/model.py',
        'core/dataset.py',
        'core/training.py',
        'core/data_loader.py',
        'core/normalizer.py',
        'core/checkpoint.py',
        'core/losses.py',
        'configs/__init__.py',
        'configs/curves.py',
        'configs/templates.py',
        'configs/validation.py',
        'api/__init__.py',
        'api/predictor.py',
        'pipelines/__init__.py',
        'pipelines/train.py',
        'pipelines/infer.py',
        'requirements.txt',
        'README.md'
    ]
    
    all_exist = True
    for file in required_files:
        if not check_file_exists(file):
            all_exist = False
    
    return all_exist

def check_import_syntax():
    """Check for common import syntax issues"""
    print("\nChecking import syntax in key files...")
    
    files_to_check = [
        'core/dataset.py',
        'core/training.py',
        'core/normalizer.py',
        'api/predictor.py',
        'pipelines/train.py',
        'pipelines/infer.py'
    ]
    
    issues_found = []
    
    for filepath in files_to_check:
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
                    line = line.strip()
                    if line.startswith('from ..core.') or line.startswith('from ..configs.') or line.startswith('from ..api.'):
                        # Check if this is within the same package
                        if filepath.startswith('core/') and '..core.' in line:
                            issues_found.append(f"{filepath}:{i} - Incorrect relative import: {line}")
                        elif filepath.startswith('api/') and '..api.' in line:
                            issues_found.append(f"{filepath}:{i} - Incorrect relative import: {line}")
    
    if issues_found:
        print("❌ Import issues found:")
        for issue in issues_found:
            print(f"  {issue}")
        return False
    else:
        print("✓ No obvious import syntax issues found")
        return True

def main():
    print("=== Codebase Health Check ===")
    
    # Check if we're in the right directory
    if not os.path.exists('core') or not os.path.exists('configs'):
        print("❌ Not in the correct directory. Please run from the project root.")
        return False
    
    # Check directory structure
    structure_ok = check_directory_structure()
    
    # Check import syntax
    imports_ok = check_import_syntax()
    
    print(f"\n=== Summary ===")
    print(f"Directory structure: {'✓ OK' if structure_ok else '❌ Issues found'}")
    print(f"Import syntax: {'✓ OK' if imports_ok else '❌ Issues found'}")
    
    if structure_ok and imports_ok:
        print("\n✅ Codebase appears to be in good shape!")
        print("The main pipeline should work correctly.")
        return True
    else:
        print("\n❌ Issues found that may prevent the pipeline from working.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
