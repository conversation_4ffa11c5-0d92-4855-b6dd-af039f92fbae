"""
Runtime configuration validation utilities
Validate templates, curve combinations, and parameters
"""

from .curves import get_curve_definition, get_supported_curves
from .templates import get_template, get_available_templates

def validate_template(template_name):
    """
    Validate that a template exists and is properly configured
    
    Args:
        template_name: Template name to validate
        
    Returns:
        bool: True if valid, False otherwise
        
    Raises:
        ValueError: If template is invalid
    """
    try:
        template = get_template(template_name)
        # Check required keys
        required_keys = ['input_curves', 'output_curves', 'model_config', 'normalization_strategy']
        for key in required_keys:
            if key not in template:
                raise ValueError(f"Template '{template_name}' missing required key: {key}")
        
        # Validate training config
        training_config = template.get('training_config', {})
        required_training_keys = ['batch_size', 'learning_rate', 'epochs', 'optimizer']
        for key in required_training_keys:
            if key not in training_config:
                raise ValueError(f"Template '{template_name}' missing training config key: {key}")
        
        # Validate model config
        if template['model_config'] not in ['mwlt_small', 'mwlt_base', 'mwlt_large']:
            raise ValueError(f"Invalid model_config '{template['model_config']}' in template '{template_name}'")
        
        return True
    except ValueError as e:
        print(f"Template validation failed: {e}")
        raise

def validate_curve_combination(input_curves, output_curves):
    """
    Validate curve combination is physically meaningful
    
    Args:
        input_curves: List of input curve names
        output_curves: List of output curve names
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    all_curves = input_curves + output_curves
    
    # Check all curves are supported
    for curve in all_curves:
        try:
            get_curve_definition(curve)
        except ValueError:
            messages.append(f"Unsupported curve: {curve}")
            is_valid = False
    
    # Check for duplicate curves
    if len(set(all_curves)) != len(all_curves):
        duplicates = [x for x in all_curves if all_curves.count(x) > 1]
        messages.append(f"Duplicate curves found: {duplicates}")
        is_valid = False
    
    # Alias conflict checks
    if 'VP' in all_curves and 'AC' in all_curves:
        messages.append("Warning: VP and AC are aliases - don't use both as they represent the same curve")
    
    if 'CNL' in all_curves and 'NPHI' in all_curves:
        messages.append("Warning: CNL and NPHI are similar neutron porosity measurements - consider using only one")
    
    if 'RLLD' in all_curves and 'RILD' in all_curves:
        messages.append("Warning: RLLD and RILD are similar deep resistivity measurements - consider using only one")
    
    return is_valid, messages

def validate_training_config(training_config):
    """
    Validate training configuration parameters
    
    Args:
        training_config: Training configuration dictionary
        
    Returns:
        bool: True if valid, False otherwise
    """
    # Check batch_size
    batch_size = training_config.get('batch_size', 32)
    if not isinstance(batch_size, int) or batch_size <= 0:
        raise ValueError(f"Invalid batch_size: {batch_size}. Must be positive integer.")
    
    # Check learning_rate
    learning_rate = training_config.get('learning_rate', 0.001)
    if not isinstance(learning_rate, (int, float)) or learning_rate <= 0:
        raise ValueError(f"Invalid learning_rate: {learning_rate}. Must be positive number.")
    
    # Check epochs
    epochs = training_config.get('epochs', 100)
    if not isinstance(epochs, int) or epochs <= 0:
        raise ValueError(f"Invalid epochs: {epochs}. Must be positive integer.")
    
    # Check optimizer
    optimizer = training_config.get('optimizer', 'adam')
    valid_optimizers = ['adam', 'sgd', 'rmsprop']
    if optimizer not in valid_optimizers:
        raise ValueError(f"Invalid optimizer: {optimizer}. Must be one of {valid_optimizers}")
    
    return True

def validate_full_configuration(template_name=None, input_curves=None, output_curves=None, training_config=None):
    """
    Comprehensive configuration validation
    
    Args:
        template_name: Template name (if using template)
        input_curves: Input curves (if not using template)
        output_curves: Output curves (if not using template)
        training_config: Training configuration (if not using template)
        
    Returns:
        dict: Validation results with status and messages
    """
    results = {
        'valid': True,
        'messages': [],
        'warnings': []
    }
    
    try:
        # Validate template if provided
        if template_name:
            validate_template(template_name)
            template = get_template(template_name)
            input_curves = template['input_curves']
            output_curves = template['output_curves']
            training_config = template.get('training_config', {})
        
        # Validate curve combination
        curve_valid, curve_messages = validate_curve_combination(input_curves, output_curves)
        results['valid'] = results['valid'] and curve_valid
        results['messages'].extend(curve_messages)
        
        # Separate warnings from errors
        warnings = [msg for msg in curve_messages if msg.startswith('Warning:')]
        errors = [msg for msg in curve_messages if not msg.startswith('Warning:')]
        results['warnings'].extend(warnings)
        results['messages'] = errors
        
        # Validate training config
        if training_config:
            try:
                validate_training_config(training_config)
            except ValueError as e:
                results['valid'] = False
                results['messages'].append(f"Training config validation failed: {e}")
        
        return results
        
    except Exception as e:
        results['valid'] = False
        results['messages'].append(f"Configuration validation failed: {e}")
        return results