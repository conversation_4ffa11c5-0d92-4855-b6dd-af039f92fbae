"""
Training manager for single-target well log prediction
Supports Adam optimizer with optional LR scheduler and gradient clipping
Integrates with checkpoint.py for robust save/load
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, List
import json
import os
from pathlib import Path
from .checkpoint import save_checkpoint, load_checkpoint
from .losses import create_curve_loss
from .dataset import SingleTargetDataset
from .model import MWLT_Base
from .utils import get_device

class TrainingManager:
    """
    Simple training manager for single-target prediction
    Adam optimizer with optional ReduceLROnPlateau scheduler
    Gradient clipping and robust checkpointing
    """
    
    def __init__(self, 
                 model: nn.Module,
                 train_dataset: SingleTargetDataset,
                 val_dataset: Optional[SingleTargetDataset] = None,
                 target_curve: str = 'VP',
                 learning_rate: float = 0.001,
                 batch_size: int = 32,
                 max_epochs: int = 100,
                 use_scheduler: bool = True,
                 scheduler_patience: int = 10,
                 grad_clip: float = 1.0,
                 save_path: str = './runs'):
        """
        Initialize TrainingManager
        
        Args:
            model: MWLT model instance
            train_dataset: Training dataset
            val_dataset: Optional validation dataset
            target_curve: Target curve name for loss configuration
            learning_rate: Initial learning rate
            batch_size: Batch size for training
            max_epochs: Maximum number of epochs
            use_scheduler: Whether to use ReduceLROnPlateau scheduler
            scheduler_patience: Patience for LR reduction
            grad_clip: Gradient clipping value
            save_path: Directory to save checkpoints and history
        """
        self.model = model
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset
        self.target_curve = target_curve
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.max_epochs = max_epochs
        self.use_scheduler = use_scheduler
        self.scheduler_patience = scheduler_patience
        self.grad_clip = grad_clip
        self.save_path = Path(save_path)
        
        # Device setup
        self.device = get_device()
        self.model.to(self.device)
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset, 
            batch_size=batch_size, 
            shuffle=True, 
            num_workers=0,  # Windows compatibility
            pin_memory=torch.cuda.is_available()
        )
        
        if val_dataset:
            self.val_loader = DataLoader(
                val_dataset, 
                batch_size=batch_size, 
                shuffle=False, 
                num_workers=0,
                pin_memory=torch.cuda.is_available()
            )
        else:
            self.val_loader = None
        
        # Setup optimizer and loss
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-5
        )
        
        self.loss_fn = create_curve_loss(target_curve)
        
        # Setup scheduler if requested
        if use_scheduler:
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                patience=scheduler_patience,
                factor=0.5,
                min_lr=1e-6,
                verbose=True
            )
        else:
            self.scheduler = None
        
        # Training state
        self.epoch = 0
        self.best_val_loss = float('inf')
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rates': []
        }
        
        # Create save directory
        self.save_path.mkdir(parents=True, exist_ok=True)
        
        print(f"TrainingManager initialized for {target_curve} prediction")
        print(f"  Train samples: {len(train_dataset)}")
        print(f"  Val samples: {len(val_dataset) if val_dataset else 0}")
        print(f"  Device: {self.device}")
    
    def train(self) -> Dict:
        """
        Run training loop
        
        Returns:
            dict: Training history and final metrics
        """
        print("Starting training...")
        
        for epoch in range(self.max_epochs):
            self.epoch = epoch
            
            # Training phase
            train_loss = self._train_epoch()
            
            # Validation phase
            val_loss = self._validate_epoch() if self.val_loader else train_loss
            
            # Learning rate scheduling
            if self.scheduler:
                self.scheduler.step(val_loss)
            
            # Record history
            current_lr = self.optimizer.param_groups[0]['lr']
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['learning_rates'].append(current_lr)
            
            # Save checkpoint
            self._save_checkpoint(epoch, train_loss, val_loss)
            
            # Check for best model
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                print(f"New best model at epoch {epoch+1}: val_loss = {val_loss:.6f}")
            
            print(f"Epoch {epoch+1:3d}/{self.max_epochs} | Train: {train_loss:.6f} | Val: {val_loss:.6f} | LR: {current_lr:.2e}")
        
        # Save final training history
        self._save_training_history()
        
        return {
            'best_val_loss': self.best_val_loss,
            'training_history': self.training_history,
            'final_epoch': self.epoch,
            'target_curve': self.target_curve
        }
    
    def _train_epoch(self) -> float:
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_inputs, batch_targets in self.train_loader:
            batch_inputs = batch_inputs.to(self.device)
            batch_targets = batch_targets.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            predictions = self.model(batch_inputs)
            loss = self.loss_fn(predictions, batch_targets)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if self.grad_clip > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.grad_clip)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / num_batches
    
    def _validate_epoch(self) -> float:
        """Validate for one epoch"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch_inputs, batch_targets in self.val_loader:
                batch_inputs = batch_inputs.to(self.device)
                batch_targets = batch_targets.to(self.device)
                
                predictions = self.model(batch_inputs)
                loss = self.loss_fn(predictions, batch_targets)
                
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def _save_checkpoint(self, epoch: int, train_loss: float, val_loss: float):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_val_loss': self.best_val_loss,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'target_curve': self.target_curve,
            'config': {
                'learning_rate': self.learning_rate,
                'batch_size': self.batch_size,
                'max_epochs': self.max_epochs,
                'use_scheduler': self.use_scheduler,
                'grad_clip': self.grad_clip
            }
        }
        
        checkpoint_path = self.save_path / f'checkpoint_epoch_{epoch+1}.pth'
        save_checkpoint(checkpoint, str(checkpoint_path))
        
        # Save best model
        if val_loss < self.best_val_loss:
            best_path = self.save_path / 'best_model.pth'
            save_checkpoint(checkpoint, str(best_path))
    
    def _save_training_history(self):
        """Save training history as JSON"""
        history_path = self.save_path / 'training_history.json'
        with open(history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2)
        
        print(f"Training history saved to {history_path}")
    
    def load_model(self, checkpoint_path: str):
        """Load model from checkpoint"""
        model_dict, epoch, loss = load_checkpoint(checkpoint_path, self.device)
        self.model.load_state_dict(model_dict)
        print(f"Model loaded from epoch {epoch} with loss {loss}")
        return epoch, loss

# Factory functions
def create_vp_trainer(train_data_path: str, 
                      val_data_path: str = None,
                      save_path: str = './runs/vp',
                      **kwargs):
    """
    Create trainer for VP prediction
    
    Args:
        train_data_path: Path to training data file
        val_data_path: Optional validation data path
        save_path: Path to save checkpoints
        **kwargs: Additional TrainingManager parameters
        
    Returns:
        TrainingManager: Configured trainer
    """
    from ..core.dataset import SingleTargetDataset
    from ..core.model import MWLT_Base
    
    # Create datasets
    train_dataset = SingleTargetDataset(train_data_path, target_curve='VP')
    val_dataset = SingleTargetDataset(val_data_path, target_curve='VP') if val_data_path else None
    
    # Create model
    model = MWLT_Base(in_channels=4, out_channels=1)  # GR, CNL, DEN, RLLD -> VP
    
    # Create trainer
    trainer = TrainingManager(
        model=model,
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        target_curve='VP',
        save_path=save_path,
        **kwargs
    )
    
    return trainer

def create_density_trainer(train_data_path: str, 
                           val_data_path: str = None,
                           save_path: str = './runs/density',
                           **kwargs):
    """
    Create trainer for density prediction
    
    Args:
        train_data_path: Path to training data file
        val_data_path: Optional validation data path
        save_path: Path to save checkpoints
        **kwargs: Additional TrainingManager parameters
        
    Returns:
        TrainingManager: Configured trainer
    """
    from ..core.dataset import SingleTargetDataset
    from ..core.model import MWLT_Base
    
    # Create datasets
    train_dataset = SingleTargetDataset(train_data_path, target_curve='DEN')
    val_dataset = SingleTargetDataset(val_data_path, target_curve='DEN') if val_data_path else None
    
    # Create model
    model = MWLT_Base(in_channels=4, out_channels=1)  # GR, CNL, VP, RLLD -> DEN
    
    # Create trainer
    trainer = TrainingManager(
        model=model,
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        target_curve='DEN',
        save_path=save_path,
        learning_rate=0.0005,  # Lower LR for density
        **kwargs
    )
    
    return trainer