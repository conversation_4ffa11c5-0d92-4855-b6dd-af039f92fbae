"""
Utility functions for well log prediction
Device detection and basic metrics (RMSE, R2, MAE)
"""

import torch
import numpy as np
from typing import Union, Dict

def get_device(device_id: int = 0) -> torch.device:
    """
    Get the appropriate device (GPU if available, otherwise CPU)
    
    Args:
        device_id: GPU device ID to use (default: 0)
        
    Returns:
        torch.device: The device to use for computation
    """
    if torch.cuda.is_available():
        device = torch.device(f"cuda:{device_id}")
        print(f"CUDA is available. Using GPU: {device}")
        if torch.cuda.is_available():
            print(f"GPU Name: {torch.cuda.get_device_name(device_id)}")
            print(f"GPU Memory: {torch.cuda.get_device_properties(device_id).total_memory / 1024**3:.1f} GB")
    else:
        device = torch.device("cpu")
        print("CUDA is not available. Using CPU for computation.")
    
    return device

def calculate_rmse(predictions: Union[np.ndarray, torch.Tensor], 
                   targets: Union[np.ndarray, torch.Tensor]) -> float:
    """
    Calculate Root Mean Square Error
    
    Args:
        predictions: Model predictions
        targets: Ground truth targets
        
    Returns:
        float: RMSE value
    """
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.detach().cpu().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.detach().cpu().numpy()
    
    mse = np.mean((predictions - targets) ** 2)
    rmse = np.sqrt(mse)
    return rmse

def calculate_r2(predictions: Union[np.ndarray, torch.Tensor], 
                 targets: Union[np.ndarray, torch.Tensor]) -> float:
    """
    Calculate R-squared (coefficient of determination)
    
    Args:
        predictions: Model predictions
        targets: Ground truth targets
        
    Returns:
        float: R² value
    """
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.detach().cpu().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.detach().cpu().numpy()
    
    ss_res = np.sum((targets - predictions) ** 2)
    ss_tot = np.sum((targets - np.mean(targets)) ** 2)
    r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0.0
    return r2

def calculate_mae(predictions: Union[np.ndarray, torch.Tensor], 
                  targets: Union[np.ndarray, torch.Tensor]) -> float:
    """
    Calculate Mean Absolute Error
    
    Args:
        predictions: Model predictions
        targets: Ground truth targets
        
    Returns:
        float: MAE value
    """
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.detach().cpu().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.detach().cpu().numpy()
    
    mae = np.mean(np.abs(predictions - targets))
    return mae

def evaluate_predictions(predictions: Union[np.ndarray, torch.Tensor], 
                        targets: Union[np.ndarray, torch.Tensor]) -> Dict[str, float]:
    """
    Compute comprehensive evaluation metrics
    
    Args:
        predictions: Model predictions
        targets: Ground truth targets
        
    Returns:
        dict: Dictionary of evaluation metrics
    """
    return {
        'rmse': calculate_rmse(predictions, targets),
        'r2': calculate_r2(predictions, targets),
        'mae': calculate_mae(predictions, targets)
    }

def count_model_parameters(model: torch.nn.Module, trainable_only: bool = False) -> Dict[str, int]:
    """
    Count model parameters
    
    Args:
        model: PyTorch model
        trainable_only: Whether to count only trainable parameters
        
    Returns:
        dict: Parameter counts
    """
    if trainable_only:
        total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        return {'trainable_parameters': total_params}
    else:
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params
        }

def ensure_dir_exists(directory: str) -> None:
    """
    Ensure directory exists, create if necessary
    
    Args:
        directory: Directory path
    """
    os.makedirs(directory, exist_ok=True)