#!/usr/bin/env python3
"""
Test script to check if all imports work correctly after moving the codebase
"""

def test_imports():
    """Test all critical imports"""
    try:
        print("Testing core imports...")
        from core.utils import get_device
        print("✓ Core utils imported")
        from core.model import MWLT_Base
        print("✓ Core model imported")
        from core.dataset import SingleTargetDataset
        print("✓ Core dataset imported")
        from core.training import TrainingManager
        print("✓ Core training imported")
        from core.data_loader import load_hdf5, load_las, load_well_data
        print("✓ Core data_loader imported")
        from core.normalizer import StandardNormalizer, ResistivityNormalizer
        print("✓ Core normalizer imported")
        from core.checkpoint import save_checkpoint, load_checkpoint
        print("✓ Core checkpoint imported")
        print("✓ Core imports successful")
        
        print("Testing config imports...")
        from configs.templates import get_template, get_available_templates
        from configs.curves import get_curve_definition, get_supported_curves
        from configs.validation import validate_template, validate_full_configuration
        print("✓ Config imports successful")
        
        print("Testing API imports...")
        from api.predictor import GeneralPredictor
        print("✓ API imports successful")
        
        print("Testing pipeline imports...")
        import pipelines.train
        import pipelines.infer
        print("✓ Pipeline imports successful")
        
        print("\nTesting device detection...")
        device = get_device()
        print(f"✓ Device detected: {device}")
        
        print("\nTesting template loading...")
        templates = get_available_templates()
        print(f"✓ Available templates: {templates}")
        
        for template_name in templates:
            template = get_template(template_name)
            print(f"✓ Template '{template_name}' loaded successfully")
            print(f"  - Input curves: {template['input_curves']}")
            print(f"  - Output curves: {template['output_curves']}")
        
        print("\n✅ All imports and basic functionality tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    exit(0 if success else 1)
