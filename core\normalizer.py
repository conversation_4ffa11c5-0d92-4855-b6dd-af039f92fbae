"""
Curve-specific normalization utilities
Implements StandardNormalizer (z-score) and LogStandardNormalizer (log10 + z-score)
Factory pattern for creating normalizers based on curve type
"""

import torch
import numpy as np
from typing import Dict, <PERSON>, List
from configs.curves import get_curve_definition

class StandardNormalizer:
    """
    Standard z-score normalization for non-resistivity curves (GR, DEN, AC)
    """
    
    def __init__(self, mean: float = 0.0, std: float = 1.0, clip_range: tuple = (-3, 3)):
        """
        Initialize StandardNormalizer
        
        Args:
            mean: Mean value for normalization (default from curve config)
            std: Standard deviation for normalization
            clip_range: Clipping range after normalization (z-score typically -3 to 3)
        """
        self.mean = mean
        self.std = std
        self.clip_range = clip_range
    
    def fit(self, data: Union[np.ndarray, torch.Tensor]) -> 'StandardNormalizer':
        """
        Fit normalizer to data (compute mean and std)
        
        Args:
            data: Training data to fit the normalizer
            
        Returns:
            Self for method chaining
        """
        if isinstance(data, torch.Tensor):
            data = data.numpy()
        
        self.mean = np.mean(data)
        self.std = np.std(data) if np.std(data) > 0 else 1.0
        return self
    
    def normalize(self, data: Union[np.ndarray, torch.Tensor]) -> torch.Tensor:
        """
        Normalize data using z-score
        
        Args:
            data: Input data to normalize
            
        Returns:
            torch.Tensor: Normalized data
        """
        if isinstance(data, np.ndarray):
            data = torch.from_numpy(data).float()
        else:
            data = data.float()
        
        normalized = (data - self.mean) / self.std
        normalized = torch.clamp(normalized, self.clip_range[0], self.clip_range[1])
        return normalized
    
    def denormalize(self, normalized_data: torch.Tensor) -> torch.Tensor:
        """
        Denormalize data back to original scale
        
        Args:
            normalized_data: Normalized data
            
        Returns:
            torch.Tensor: Denormalized data
        """
        denormalized = normalized_data * self.std + self.mean
        return denormalized

class LogStandardNormalizer:
    """
    Log10 + z-score normalization for resistivity curves (RLLD, RILD)
    Handles log-domain processing and clamping to avoid log(0) or negative values
    """
    
    def __init__(self, mean: float = 0.0, std: float = 1.0, clip_range: tuple = (-3, 3), log_min: float = 0.1):
        """
        Initialize LogStandardNormalizer
        
        Args:
            mean: Mean of log10 values
            std: Standard deviation of log10 values
            clip_range: Clipping range after log-zscore
            log_min: Minimum value before log10 (to avoid log(0))
        """
        self.mean = mean
        self.std = std
        self.clip_range = clip_range
        self.log_min = log_min
    
    def fit(self, data: Union[np.ndarray, torch.Tensor]) -> 'LogStandardNormalizer':
        """
        Fit normalizer to data (compute log10 mean and std)
        
        Args:
            data: Training data to fit the normalizer
            
        Returns:
            Self for method chaining
        """
        if isinstance(data, torch.Tensor):
            data = data.numpy()
        
        # Clamp and take log10
        log_data = np.log10(np.clip(data, self.log_min, np.inf))
        self.mean = np.mean(log_data)
        self.std = np.std(log_data) if np.std(log_data) > 0 else 1.0
        return self
    
    def normalize(self, data: Union[np.ndarray, torch.Tensor]) -> torch.Tensor:
        """
        Normalize data using log10 + z-score
        
        Args:
            data: Input data to normalize (resistivity values)
            
        Returns:
            torch.Tensor: Normalized log-zscore data
        """
        if isinstance(data, np.ndarray):
            data = torch.from_numpy(data).float()
        else:
            data = data.float()
        
        # Clamp to avoid log issues
        clamped_data = torch.clamp(data, self.log_min, torch.inf)
        log_data = torch.log10(clamped_data)
        
        # Z-score normalization on log values
        normalized = (log_data - self.mean) / self.std
        normalized = torch.clamp(normalized, self.clip_range[0], self.clip_range[1])
        
        return normalized
    
    def denormalize(self, normalized_data: torch.Tensor) -> torch.Tensor:
        """
        Denormalize log-zscore data back to resistivity values
        
        Args:
            normalized_data: Normalized log-zscore data
            
        Returns:
            torch.Tensor: Denormalized resistivity values
        """
        # Reverse z-score to get log values
        log_data = normalized_data * self.std + self.mean
        
        # Reverse log10 to get original values
        denormalized = torch.pow(10, log_data)
        
        # Clamp to reasonable resistivity range
        denormalized = torch.clamp(denormalized, self.log_min, 10000.0)
        
        return denormalized

class CurveNormalizerFactory:
    """
    Factory for creating curve-specific normalizers based on curve type and strategy
    """
    
    @staticmethod
    def create_normalizer(curve_name: str, strategy: str = None) -> Union[StandardNormalizer, LogStandardNormalizer]:
        """
        Create appropriate normalizer for a given curve
        
        Args:
            curve_name: Name of the curve (e.g., 'GR', 'DEN', 'RLLD', 'VP')
            strategy: Normalization strategy override (optional)
            
        Returns:
            Normalizer instance (StandardNormalizer or LogStandardNormalizer)
        """
        curve_name = curve_name.upper()
        
        try:
            curve_def = get_curve_definition(curve_name)
            norm_strategy = strategy or curve_def['normalization']
            
            if norm_strategy == 'standard':
                # Z-score for non-resistivity curves (GR, DEN, AC/VP)
                mean = curve_def.get('mean', 0.0)  # Will be fitted or from config
                std = curve_def.get('std', 1.0)
                return StandardNormalizer(mean=mean, std=std)
            
            elif norm_strategy == 'log_standard':
                # Log10 + z-score for resistivity curves (RLLD, RILD)
                mean = curve_def.get('mean', 1.0)  # Mean of log10 values
                std = curve_def.get('std', 2.0)
                return LogStandardNormalizer(mean=mean, std=std)
            
            else:
                raise ValueError(f"Unknown normalization strategy '{norm_strategy}' for curve '{curve_name}'")
                
        except ValueError as e:
            print(f"Warning: {e}. Using StandardNormalizer with defaults.")
            return StandardNormalizer()
    
    @staticmethod
    def create_normalizers_for_curves(curve_names: List[str]) -> Dict[str, Union[StandardNormalizer, LogStandardNormalizer]]:
        """
        Create normalizers for multiple curves
        
        Args:
            curve_names: List of curve names
            
        Returns:
            dict: Dictionary mapping curve names to their normalizers
        """
        normalizers = {}
        for curve_name in curve_names:
            normalizers[curve_name] = CurveNormalizerFactory.create_normalizer(curve_name)
        return normalizers
    
    @staticmethod
    def fit_all_normalizers(curve_data: Dict[str, Union[np.ndarray, torch.Tensor]], 
                           normalizers: Dict[str, Union[StandardNormalizer, LogStandardNormalizer]]) -> None:
        """
        Fit all normalizers to their respective training data
        
        Args:
            curve_data: Dictionary of raw curve data
            normalizers: Dictionary of normalizer instances
        """
        for curve_name, data in curve_data.items():
            if curve_name in normalizers:
                normalizers[curve_name].fit(data)

# Convenience functions
def create_curve_normalizer(curve_name: str, strategy: str = None) -> Union[StandardNormalizer, LogStandardNormalizer]:
    """
    Convenience function to create a curve-specific normalizer
    
    Args:
        curve_name: Name of the curve
        strategy: Override strategy (optional)
        
    Returns:
        Normalizer instance
    """
    return CurveNormalizerFactory.create_normalizer(curve_name, strategy)

def normalize_curve(data: Union[np.ndarray, torch.Tensor], curve_name: str) -> torch.Tensor:
    """
    Normalize a single curve using appropriate method
    
    Args:
        data: Raw curve data
        curve_name: Name of the curve
        
    Returns:
        torch.Tensor: Normalized data
    """
    normalizer = create_curve_normalizer(curve_name)
    # Fit if we have training data, but for inference, use pre-fitted or defaults
    return normalizer.normalize(data)

def denormalize_curve(normalized_data: torch.Tensor, curve_name: str) -> torch.Tensor:
    """
    Denormalize a curve back to physical units
    
    Args:
        normalized_data: Normalized data
        curve_name: Name of the curve
        
    Returns:
        torch.Tensor: Denormalized data
    """
    normalizer = create_curve_normalizer(curve_name)
    return normalizer.denormalize(normalized_data)