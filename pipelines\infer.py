"""
CLI inference pipeline for single-target well log prediction
Supports 'vp_prediction' and 'density_prediction' templates
"""

import argparse
import torch
import numpy as np
import json
from pathlib import Path
from ..core.utils import get_device
from ..core.data_loader import load_well_data
from ..api.predictor import GeneralPredictor

def parse_arguments():
    parser = argparse.ArgumentParser(description='Inference for well log model')
    parser.add_argument('--template', required=True, choices=['vp_prediction', 'density_prediction'],
                        help='Template for inference (vp_prediction or density_prediction)')
    parser.add_argument('--model_path', required=True,
                        help='Path to trained model checkpoint')
    parser.add_argument('--input_file', required=True,
                        help='Path to input data file (HDF5 or LAS)')
    parser.add_argument('--output_path', required=True,
                        help='Path to save prediction results')
    parser.add_argument('--save_format', default='json', choices=['json', 'numpy', 'csv'],
                        help='Format to save predictions')
    
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    print(f"Inference configuration:")
    print(f"  Template: {args.template}")
    print(f"  Model path: {args.model_path}")
    print(f"  Input file: {args.input_file}")
    print(f"  Output path: {args.output_path}")
    
    # Device setup
    device = get_device()
    
    # Create predictor
    predictor = GeneralPredictor.from_template(
        template_name=args.template,
        model_path=args.model_path,
        device_id=0 if 'cuda' in str(device) else -1
    )
    
    print("Loading input data...")
    # Load input data (without target for inference)
    input_curves = predictor.template['input_curves']
    input_data = load_well_data(args.input_file, curves=input_curves)
    
    print("Running inference...")
    predictions = predictor.predict_from_curves(input_data)
    
    # Save predictions
    output_dir = Path(args.output_path)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if args.save_format == 'json':
        # Save as JSON with metadata
        results = {
            'template': args.template,
            'input_curves': input_curves,
            'output_curves': predictor.output_curves,
            'predictions': {
                curve: pred.tolist() for curve, pred in predictions.items()
            },
            'model_info': predictor.get_model_info(),
            'configuration': predictor.get_configuration()
        }
        output_file = output_dir / 'predictions.json'
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"Predictions saved to {output_file}")
    
    elif args.save_format == 'numpy':
        # Save as .npy files
        for curve, pred in predictions.items():
            np.save(output_dir / f'{curve}_prediction.npy', pred)
        print(f"Predictions saved to {output_dir}/*.npy")
    
    elif args.save_format == 'csv':
        # Save as CSV for easy viewing
        import pandas as pd
        df_data = {}
        df_data['depth'] = np.arange(len(list(predictions.values())[0]))  # Assume depth index
        for curve, pred in predictions.items():
            df_data[curve] = pred
        
        df = pd.DataFrame(df_data)
        output_file = output_dir / 'predictions.csv'
        df.to_csv(output_file, index=False)
        print(f"Predictions saved to {output_file}")
    
    print("Inference completed successfully!")

if __name__ == "__main__":
    main()