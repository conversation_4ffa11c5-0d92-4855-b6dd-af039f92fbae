#!/usr/bin/env python3
"""
Quick test to verify the typing import fix
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

def test_core_utils_import():
    """Test the specific import that was failing"""
    try:
        print("Testing core.utils import...")
        from core.utils import get_device, calculate_rmse, calculate_r2, calculate_mae
        print("✅ core.utils import successful!")
        
        # Test device detection
        device = get_device()
        print(f"✅ Device detection works: {device}")
        
        # Test metric functions with dummy data
        import numpy as np
        pred = np.array([1.0, 2.0, 3.0])
        true = np.array([1.1, 2.1, 2.9])
        
        rmse = calculate_rmse(pred, true)
        r2 = calculate_r2(pred, true)
        mae = calculate_mae(pred, true)
        
        print(f"✅ Metrics calculation works:")
        print(f"   RMSE: {rmse:.4f}")
        print(f"   R²: {r2:.4f}")
        print(f"   MAE: {mae:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ core.utils import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_other_imports():
    """Test other key imports"""
    try:
        print("\nTesting other key imports...")
        
        from core.dataset import SingleTargetDataset
        print("✅ core.dataset import successful!")
        
        from core.training import TrainingManager
        print("✅ core.training import successful!")
        
        from core.model import MWLT_Base
        print("✅ core.model import successful!")
        
        from api.predictor import GeneralPredictor
        print("✅ api.predictor import successful!")
        
        from configs.templates import get_available_templates, get_template
        templates = get_available_templates()
        print(f"✅ configs.templates import successful! Available: {templates}")
        
        return True
        
    except Exception as e:
        print(f"❌ Other imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔧 Quick Import Test - Verifying Typing Fix")
    print("=" * 50)
    
    # Test the specific import that was failing
    utils_ok = test_core_utils_import()
    
    # Test other imports
    other_ok = test_other_imports()
    
    print("\n" + "=" * 50)
    if utils_ok and other_ok:
        print("🎉 All imports successful! The main test should now work.")
        print("You can now run: python test_complete_workflow.py")
        return 0
    else:
        print("❌ Some imports still failing. Check the errors above.")
        return 1

if __name__ == "__main__":
    exit(main())
