"""
Complete API Usage Examples for GeneralPredictor
Location: api/predictor.py
"""

import numpy as np
from api.predictor import GeneralPredictor

# =============================================================================
# METHOD 1: Using the Factory Method (Recommended)
# =============================================================================

def example_1_factory_method():
    """Example using the from_template factory method"""
    
    # Create predictor for VP (acoustic) prediction
    vp_predictor = GeneralPredictor.from_template(
        template_name='vp_prediction',
        model_path='./runs/vp_model/best_model.pth',
        device_id=0  # Use GPU 0, or -1 for CPU
    )
    
    # Create predictor for density prediction
    density_predictor = GeneralPredictor.from_template(
        template_name='density_prediction',
        model_path='./runs/density_model/best_model.pth',
        device_id=-1  # Use CPU
    )
    
    return vp_predictor, density_predictor

# =============================================================================
# METHOD 2: Direct Initialization
# =============================================================================

def example_2_direct_init():
    """Example using direct initialization"""
    
    predictor = GeneralPredictor(
        model_path='./runs/vp_model/best_model.pth',
        template_name='vp_prediction',
        device_id=0
    )
    
    return predictor

# =============================================================================
# MAKING PREDICTIONS
# =============================================================================

def example_3_making_predictions():
    """Example of making predictions with curve data"""
    
    # Initialize predictor
    predictor = GeneralPredictor.from_template(
        template_name='vp_prediction',
        model_path='./runs/vp_model/best_model.pth'
    )
    
    # Prepare input data (example with 1000 data points)
    sequence_length = 1000
    
    # VP prediction requires: ['GR', 'CNL', 'DEN', 'RLLD']
    input_curves = {
        'GR': np.random.uniform(20, 150, sequence_length),      # Gamma Ray (API)
        'CNL': np.random.uniform(0.1, 0.4, sequence_length),   # Neutron Porosity
        'DEN': np.random.uniform(1.8, 2.8, sequence_length),   # Bulk Density (g/cc)
        'RLLD': np.random.uniform(1, 100, sequence_length)     # Resistivity (ohm.m)
    }
    
    # Make prediction
    predictions = predictor.predict_from_curves(input_curves)
    
    # predictions is a dictionary: {'VP': numpy_array}
    vp_prediction = predictions['VP']
    print(f"VP prediction shape: {vp_prediction.shape}")
    print(f"VP prediction range: {vp_prediction.min():.2f} - {vp_prediction.max():.2f}")
    
    return predictions

def example_4_density_prediction():
    """Example of density prediction"""
    
    # Initialize predictor for density
    predictor = GeneralPredictor.from_template(
        template_name='density_prediction',
        model_path='./runs/density_model/best_model.pth'
    )
    
    # Density prediction requires: ['GR', 'CNL', 'VP', 'RLLD']
    sequence_length = 1000
    input_curves = {
        'GR': np.random.uniform(20, 150, sequence_length),      # Gamma Ray (API)
        'CNL': np.random.uniform(0.1, 0.4, sequence_length),   # Neutron Porosity
        'VP': np.random.uniform(50, 180, sequence_length),      # P-wave velocity (us/ft)
        'RLLD': np.random.uniform(1, 100, sequence_length)     # Resistivity (ohm.m)
    }
    
    # Make prediction
    predictions = predictor.predict_from_curves(input_curves)
    
    # predictions is a dictionary: {'DEN': numpy_array}
    density_prediction = predictions['DEN']
    print(f"Density prediction shape: {density_prediction.shape}")
    print(f"Density prediction range: {density_prediction.min():.2f} - {density_prediction.max():.2f}")
    
    return predictions

# =============================================================================
# UTILITY METHODS
# =============================================================================

def example_5_utility_methods():
    """Example of using utility methods"""
    
    predictor = GeneralPredictor.from_template(
        template_name='vp_prediction',
        model_path='./runs/vp_model/best_model.pth'
    )
    
    # Get model information
    model_info = predictor.get_model_info()
    print("Model Info:", model_info)
    
    # Get configuration
    config = predictor.get_configuration()
    print("Configuration:")
    print(f"  Template: {config['template_name']}")
    print(f"  Input curves: {config['input_curves']}")
    print(f"  Output curves: {config['output_curves']}")
    print(f"  Device: {config['device']}")
    
    return config

# =============================================================================
# COMPLETE WORKFLOW EXAMPLE
# =============================================================================

def complete_workflow_example():
    """Complete workflow from data loading to prediction"""
    
    print("=== Complete VP Prediction Workflow ===")
    
    # Step 1: Initialize predictor
    print("1. Initializing predictor...")
    predictor = GeneralPredictor.from_template(
        template_name='vp_prediction',
        model_path='./runs/vp_model/best_model.pth',
        device_id=0
    )
    
    # Step 2: Check what curves are needed
    print("2. Required input curves:", predictor.input_curves)
    print("   Output curves:", predictor.output_curves)
    
    # Step 3: Prepare your data
    print("3. Preparing input data...")
    # In real usage, you would load this from HDF5/LAS files
    sequence_length = 720  # Standard window size
    
    input_data = {
        'GR': np.random.uniform(20, 150, sequence_length),
        'CNL': np.random.uniform(0.1, 0.4, sequence_length),
        'DEN': np.random.uniform(1.8, 2.8, sequence_length),
        'RLLD': np.random.uniform(1, 100, sequence_length)
    }
    
    # Step 4: Make prediction
    print("4. Making prediction...")
    predictions = predictor.predict_from_curves(input_data)
    
    # Step 5: Process results
    print("5. Processing results...")
    vp_pred = predictions['VP']
    print(f"   VP prediction statistics:")
    print(f"   - Shape: {vp_pred.shape}")
    print(f"   - Range: {vp_pred.min():.2f} - {vp_pred.max():.2f} us/ft")
    print(f"   - Mean: {vp_pred.mean():.2f} us/ft")
    
    return predictions

# =============================================================================
# TEMPLATE INFORMATION
# =============================================================================

def show_available_templates():
    """Show available templates and their configurations"""
    
    from configs.templates import get_available_templates, get_template
    
    print("=== Available Templates ===")
    templates = get_available_templates()
    
    for template_name in templates:
        template = get_template(template_name)
        print(f"\nTemplate: {template_name}")
        print(f"  Input curves: {template['input_curves']}")
        print(f"  Output curves: {template['output_curves']}")
        print(f"  Model config: {template['model_config']}")

if __name__ == "__main__":
    # Run examples
    print("Available templates:")
    show_available_templates()
    
    print("\n" + "="*50)
    print("Complete workflow example:")
    complete_workflow_example()
