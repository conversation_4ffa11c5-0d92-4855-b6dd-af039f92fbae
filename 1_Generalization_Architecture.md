# Generalized Curves Prediction Architecture (init_generalization)

Objective
- Create a new top-level folder init_generalization that hosts a generalized, configuration-driven ML stack to predict any well-log curve (Vp/AC, DEN, CNL/NPHI, GR, RLLD, and others) using one codebase.
- Reuse proven modules from init_vp_pred and init_density_base, and consolidate them under a clean package structure with backward compatibility.

Context and sources
- Existing implementations:
  - init_vp_pred\vp_predictor: modular, generalizable components (transformer, decoder, dataset, normalizer, losses, trainer, configs, API, LAS/HDF5 I/O)
  - init_density_base: robust checkpoint I/O and a cleaned MWLT variant (notably, decoder without forced output activation)
- References used via Context7 (PyTorch): Conv1d, LayerNorm, MultiheadAttention, Dataset/DataLoader validation for architecture choices.

Proposed package layout (minimalist v1)
- init_generalization/
  - core/
    - model.py            (ResCNN + Transformer backbone + 1D decoder; single-target)
    - dataset.py          (SingleTargetDataset: windowing 720→640, step_size defaults to 50%)
    - normalizer.py       (StandardNormalizer: z-score; ResistivityNormalizer: log10 + z-score)
    - losses.py           (MSE/MAE + optional simple physics-range penalty)
    - training.py         (TrainingManager: Adam, optional ReduceLROnPlateau, grad clipping)
    - checkpoint.py       (robust save/load checkpoint; adopted from density utils)
    - utils.py            (get_device, RMSE/R²/MAE helpers)
    - data.py             (load_hdf5, load_las; aliasing; gap interpolation; optional SciPy PCHIP)
  - configs/
    - curves.py           (AC/VP, DEN, GR, RLLD/RILD: units, ranges, normalization params, aliases)
    - templates.py        (only two templates: 'vp' and 'density')
  - api/
    - predictor.py        (GeneralPredictor: single-target, uses unified loaders & normalizers)
  - pipelines/
    - train.py            (CLI: single-target train for 'vp' or 'density')
    - infer.py            (CLI: single-target inference from HDF5/LAS)
  - tests/
    - test_dataset.py     (windowing counts; normalization roundtrip)
    - test_training.py    (tiny CPU smoke for AC and DEN)
  - README.md
  - __init__.py

Source → destination mapping (minimalist v1)
- From init_vp_pred\vp_predictor\core
  - model.py / transformer.py / decoder.py → core/model.py (consolidated backbone + decoder)
  - dataset.py → core/dataset.py
  - normalizer.py → core/normalizer.py
  - loss_functions.py → core/losses.py
  - training.py → core/training.py
- From init_vp_pred\vp_predictor
  - utils.py → core/utils.py (device + basic metrics)
  - las_processor.py → core/data.py (unified HDF5/LAS loaders)
  - api/predictor.py → api/predictor.py (update to GeneralPredictor and new imports)
- From init_vp_pred\vp_predictor\configs
  - curves.py → configs/curves.py (keep minimal curve metadata and normalization params)
  - (create) configs/templates.py → two templates: 'vp' and 'density'
- From init_density_base\core
  - utils.py: robust load_checkpoint/save_checkpoint → core/checkpoint.py (wire into training.py)
- Optional references
  - init_vp_pred\vp_predictor\model.py and init_density_base\core\model.py are legacy consolidated MWLT definitions. We rely on core/model.py + normalizer.py + losses.py only. Thin wrappers are not required in v1.

Key design decisions (minimalist v1)
- Single-target only (AC/VP or DEN per run)
- One backbone: ResCNN + Transformer encoder + 1D decoder; no multi-curve heads/attention
- Data formats: HDF5 and LAS via unified loaders (load_hdf5, load_las)
- Normalization: StandardNormalizer (z-score) for GR/DEN/AC; ResistivityNormalizer (log10 + z-score) for RLLD
- Missing curves policy: short gaps interpolated (PCHIP or linear fallback), long gaps skipped or default-filled; resistivity interpolated in log-domain; clip to physics ranges
- Training: Adam optimizer, optional ReduceLROnPlateau, gradient clipping; AMP off by default; robust checkpoint I/O
- Templates: only 'vp' and 'density' templates
- No complex validation layer; minimal configs only

Detailed migration plan (2–3 days, minimalist)
Day 1: Skeleton & consolidation
- Create init_generalization layout (core, configs, api, pipelines, tests); add __init__.py and README
- Consolidate core/model.py from existing transformer/decoder code; keep single-target only
- Port dataset.py (windowing 720→640 with 50% overlap); no multi-target logic
Day 2: Checkpoints, loaders, API
- Implement core/checkpoint.py from density utils (robust save/load; weights_only handling)
- Implement core/data.py with load_hdf5/load_las, aliasing (VP→AC, RILD→RLLD), and gap interpolation (PCHIP if SciPy, else linear)
- Implement normalization (StandardNormalizer, ResistivityNormalizer) with configs/curves.py
- Implement api/predictor.py (GeneralPredictor) and minimal configs/templates.py ('vp','density')
Day 3: Pipelines, tests, polish
- Add pipelines/train.py and pipelines/infer.py CLIs
- Add tests: test_dataset.py (windowing), test_training.py (tiny CPU smoke for AC/DEN)
- Run smoke tests on A1/A2 locally; adjust defaults if needed

Planned changes (minimal, focused)
- Update imports to init_generalization.core.* and configs.*
- Remove multi-curve heads/attention and complex validation layers
- Ensure TrainingManager uses checkpoint.py and writes training_history.json
- Dataset defaults: total_length=720, effective_length=640, step_size defaults to 50% overlap
- Normalization: z-score for GR/DEN/AC; log10 + z-score for RLLD; params from configs

Backward compatibility plan
- Treat AC and VP as aliases; RLLD and RILD as aliases in loaders and configs
- Provide only 'vp' and 'density' templates (no complex template system)
- Checkpoint loader accepts both raw state_dict and wrapped checkpoints with metadata

Quick start (minimalist v1)
- Train Vp (AC)
  ```bash
  python -m init_generalization.pipelines.train --template vp \
    --train_file A1.hdf5 --val_file A1.hdf5 \
    --save_path ./runs/vp
  ```
- Train DEN
  ```bash
  python -m init_generalization.pipelines.train --template density \
    --train_file A1.hdf5 --val_file A1.hdf5 \
    --save_path ./runs/density
  ```
- Predict with API
  ```python
  from init_generalization.api.predictor import GeneralPredictor
  predictor = GeneralPredictor(template='vp', model_path='./runs/vp/best_model.pth')
  result = predictor.predict_from_curves({'GR': gr, 'CNL': cnl, 'DEN': den, 'RLLD': rlld})
  ```

Similarity and difference summary (minimalist v1)
- Retained: MWLT-style backbone (ResCNN + Transformer), windowing 720→640, Adam + optional plateau scheduler, RMSE/R²
- Simplified: single-target only; minimal configs and templates ('vp','density')
- Unified: robust checkpoint I/O from density path; unified HDF5/LAS loaders with interpolation policy
- Removed: multi-curve decoders/attention, extensive validation layers, complex template systems

Acceptance criteria (minimalist v1)
- Train AC (vp) and DEN with the new pipelines on A1/A2 to stable loss curves and plausible metrics (RMSE/R²) on CPU
- Switching target curve requires only changing between 'vp' and 'density' templates
- Robust checkpoint save/load works on Windows; loader accepts both raw state_dict and wrapped checkpoints
- API supports curves dict and files (HDF5/LAS) with alias handling and gap interpolation

Risks and mitigations (minimalist v1)
- Interpolation artifacts: prefer PCHIP/Akima when SciPy available; fall back to linear; clip to physics ranges
- Resistivity edge cases: clamp ≤0 before log; interpolate in log-domain; denormalize with 10**y
- Missing inputs: fill with sensible defaults for inference; skip windows or raise for training target
- Windows paths: DataLoader num_workers=0; robust checkpoint handles permissions and weights_only differences
- Scope creep: keep to single-target and two templates; defer multi-curve and complex validators

Roadmap (minimalist v1)
- Day 1: Skeleton + consolidation (model.py, dataset.py, losses.py; minimal configs)
- Day 2: Checkpoints + loaders + API (checkpoint.py, data.py, normalizer.py, predictor.py; 'vp' and 'density' templates)
- Day 3: Pipelines + tests + smoke (train.py, infer.py, test_dataset.py, test_training.py); run A1/A2 smoke and tune defaults

References
- PyTorch nn.Conv1d, LayerNorm, Dataset/DataLoader docs
- SciPy interpolate (PCHIP/Akima) optional reference for interpolation methods
- lasio (optional) for LAS reading

