"""
Unified data loading utilities for HDF5 and LAS files
Supports aliasing, gap interpolation, and physics range clipping
"""

import numpy as np
import h5py
import os
from typing import Dict, List, Optional, Tuple
from scipy import interpolate
from scipy.interpolate import PchipInterpolator
from configs.curves import get_curve_definition, get_supported_curves

class DataLoader:
    """
    Unified data loader for well log data from HDF5 and LAS files
    Handles aliasing, gap interpolation, and data validation
    """
    
    def __init__(self, required_curves: List[str] = None):
        """
        Initialize DataLoader
        
        Args:
            required_curves: List of required curve names (optional)
        """
        self.required_curves = required_curves or get_supported_curves()
    
    def load_hdf5(self, file_path: str, curves: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
        """
        Load data from HDF5 file
        
        Args:
            file_path: Path to HDF5 file
            curves: Specific curves to load (optional, loads all if None)
            
        Returns:
            dict: Dictionary of curve names to data arrays
            
        Raises:
            FileNotFoundError: If file doesn't exist
            KeyError: If required curves are missing
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"HDF5 file not found: {file_path}")
        
        curves_data = {}
        with h5py.File(file_path, 'r') as f:
            available_curves = list(f.keys())
            print(f"Available curves in {file_path}: {available_curves}")
            
            if curves is None:
                # Load all curves
                for curve_name in available_curves:
                    data = f[curve_name][:]
                    if data.ndim > 1:
                        data = data.squeeze()
                    curves_data[curve_name] = data
            else:
                # Load specific curves
                for curve_name in curves:
                    if curve_name in available_curves:
                        data = f[curve_name][:]
                        if data.ndim > 1:
                            data = data.squeeze()
                        curves_data[curve_name] = data
                    else:
                        print(f"Warning: Curve {curve_name} not found in HDF5 file")
        
        # Apply aliasing and validation
        return self._process_curves(curves_data)
    
    def load_las(self, file_path: str, curves: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
        """
        Load data from LAS file using lasio
        
        Args:
            file_path: Path to LAS file
            curves: Specific curves to load (optional)
            
        Returns:
            dict: Dictionary of curve names to data arrays
        """
        try:
            import lasio
        except ImportError:
            raise ImportError("lasio library required for LAS file reading. Install with: pip install lasio")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"LAS file not found: {file_path}")
        
        las = lasio.read(file_path)
        curves_data = {}
        
        # Extract curves
        for curve in las.curves:
            curve_name = curve.mnemonic
            data = las[curve.mnemonic]
            curves_data[curve_name] = np.array(data)
        
        print(f"Loaded LAS file {file_path} with curves: {list(curves_data.keys())}")
        
        # Load specific curves if specified
        if curves:
            filtered_data = {}
            for curve in curves:
                if curve in curves_data:
                    filtered_data[curve] = curves_data[curve]
                else:
                    print(f"Warning: Curve {curve} not found in LAS file")
                    # Add missing curve with default values
                    seq_len = len(list(filtered_data.values())[0]) if filtered_data else 1000
                    filtered_data[curve] = np.full(seq_len, np.nan)
            curves_data = filtered_data
        
        # Apply aliasing and validation
        return self._process_curves(curves_data)
    
    def _process_curves(self, curves_data: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        Process loaded curves: apply aliasing, interpolation, validation
        
        Args:
            curves_data: Raw curves dictionary
            
        Returns:
            dict: Processed curves dictionary
        """
        processed_data = {}
        aliases = {}
        
        # Apply aliasing from configurations
        for curve_name, data in curves_data.items():
            try:
                curve_def = get_curve_definition(curve_name)
                # Use primary name, store aliases
                primary_name = curve_name
                aliases[curve_name] = curve_def.get('aliases', [])
                processed_data[primary_name] = data
            except ValueError:
                # Unknown curve, keep as is
                processed_data[curve_name] = data
                print(f"Warning: Unknown curve {curve_name}, keeping as is")
        
        # Handle missing required curves
        for required_curve in self.required_curves:
            if required_curve not in processed_data:
                print(f"Warning: Required curve {required_curve} missing. Filling with interpolated/default values.")
                seq_len = self._get_sequence_length(processed_data)
                processed_data[required_curve] = self._create_default_curve(required_curve, seq_len)
        
        # Apply gap interpolation and clipping for each curve
        for curve_name, data in processed_data.items():
            # Find gaps (NaN values)
            nan_mask = np.isnan(data)
            if np.any(nan_mask):
                print(f"Interpolating gaps in curve {curve_name}: {np.sum(nan_mask)} points")
                data = self._interpolate_gaps(data, curve_name)
            
            # Clip to physics range
            try:
                curve_def = get_curve_definition(curve_name)
                physics_min, physics_max = curve_def['physics_range']
                data = np.clip(data, physics_min, physics_max)
                print(f"Clipped {curve_name} to physics range [{physics_min}, {physics_max}]")
            except ValueError:
                print(f"Warning: No physics range defined for {curve_name}, skipping clipping")
            
            processed_data[curve_name] = data
        
        return processed_data
    
    def _interpolate_gaps(self, data: np.ndarray, curve_name: str) -> np.ndarray:
        """
        Interpolate gaps in curve data using appropriate method
        
        Args:
            data: Curve data with possible NaN gaps
            curve_name: Name of the curve
            
        Returns:
            Interpolated data array
        """
        # Create index array
        indices = np.arange(len(data))
        nan_mask = np.isnan(data)
        
        if not np.any(nan_mask):
            return data
        
        try:
            curve_def = get_curve_definition(curve_name)
            strategy = curve_def.get('interpolation_strategy', 'linear')
            
            if strategy == 'pchip' and self._has_scipy_pchip():
                # Use PCHIP interpolation (preferred for monotonicity)
                interp_func = PchipInterpolator(indices[~nan_mask], data[~nan_mask])
                interpolated_data = interp_func(indices)
            elif strategy == 'log_domain_linear' and curve_name in ['RLLD', 'RILD']:
                # Log-domain interpolation for resistivity
                log_data = np.log10(np.clip(data, 0.1, np.inf))
                log_indices = indices[~np.isnan(log_data)]
                log_valid_data = log_data[~np.isnan(log_data)]
                
                if len(log_valid_data) > 1:
                    interp_func = interpolate.interp1d(log_indices, log_valid_data, 
                                                     kind='linear', bounds_error=False, fill_value='extrapolate')
                    log_interpolated = interp_func(indices)
                    interpolated_data = 10 ** np.clip(log_interpolated, 0, 3)  # Clip log range
                else:
                    interpolated_data = np.full_like(data, 1.0)  # Default resistivity
            else:
                # Linear interpolation (fallback)
                interp_func = interpolate.interp1d(indices[~nan_mask], data[~nan_mask], 
                                                 kind='linear', bounds_error=False, fill_value='extrapolate')
                interpolated_data = interp_func(indices)
            
            # Fill any remaining NaNs with nearest valid value
            interpolated_data = self._fill_remaining_nans(interpolated_data)
            
            return interpolated_data
            
        except Exception as e:
            print(f"Interpolation failed for {curve_name}: {e}. Using linear interpolation.")
            # Fallback to linear
            nan_indices = indices[~nan_mask]
            valid_data = data[~nan_mask]
            if len(valid_data) > 1:
                interp_func = interpolate.interp1d(nan_indices, valid_data, 
                                                 kind='linear', bounds_error=False, fill_value='extrapolate')
                interpolated_data = interp_func(indices)
            else:
                interpolated_data = np.full_like(data, np.nanmean(data))
            
            return self._fill_remaining_nans(interpolated_data)
    
    def _fill_remaining_nans(self, data: np.ndarray) -> np.ndarray:
        """
        Fill any remaining NaN values with forward/backward fill, then mean
        """
        # Forward fill
        data = np.where(np.isnan(data), np.nan, data)
        forward_filled = np.copy(data)
        for i in range(1, len(data)):
            if np.isnan(forward_filled[i]) and not np.isnan(forward_filled[i-1]):
                forward_filled[i] = forward_filled[i-1]
        
        # Backward fill
        backward_filled = np.copy(forward_filled)
        for i in range(len(data)-2, -1, -1):
            if np.isnan(backward_filled[i]) and not np.isnan(backward_filled[i+1]):
                backward_filled[i] = backward_filled[i+1]
        
        # Fill remaining NaNs with mean
        data_mean = np.nanmean(backward_filled)
        final_data = np.where(np.isnan(backward_filled), data_mean, backward_filled)
        
        return final_data
    
    def _create_default_curve(self, curve_name: str, seq_len: int) -> np.ndarray:
        """
        Create default curve data for missing required curves
        
        Args:
            curve_name: Name of missing curve
            seq_len: Desired sequence length
            
        Returns:
            np.ndarray: Default curve data
        """
        try:
            curve_def = get_curve_definition(curve_name)
            physics_range = curve_def['physics_range']
            default_value = np.mean(physics_range)
            return np.full(seq_len, default_value)
        except ValueError:
            # Fallback defaults
            defaults = {
                'GR': 75.0,
                'CNL': 20.0,
                'DEN': 2.5,
                'RLLD': 10.0,
                'VP': 100.0
            }
            return np.full(seq_len, defaults.get(curve_name, 0.0))
    
    def _get_sequence_length(self, curves_data: Dict[str, np.ndarray]) -> int:
        """
        Get sequence length from curves data
        
        Args:
            curves_data: Dictionary of curves
            
        Returns:
            int: Sequence length
        """
        if not curves_data:
            return 1000  # Default length
        
        lengths = [len(data) for data in curves_data.values()]
        if len(set(lengths)) == 1:
            return lengths[0]
        else:
            print("Warning: Curves have different lengths. Using maximum length.")
            return max(lengths)
    
    def _has_scipy_pchip(self) -> bool:
        """
        Check if SciPy PCHIP interpolation is available
        
        Returns:
            bool: True if available
        """
        try:
            from scipy.interpolate import PchipInterpolator
            return True
        except ImportError:
            print("SciPy PCHIP not available. Using linear interpolation.")
            return False


# Convenience functions for direct usage
def load_hdf5(file_path: str, curves: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
    """
    Load data from HDF5 file (convenience function)
    
    Args:
        file_path: Path to HDF5 file
        curves: Specific curves to load
        
    Returns:
        dict: Processed curves dictionary
    """
    loader = DataLoader()
    return loader.load_hdf5(file_path, curves)

def load_las(file_path: str, curves: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
    """
    Load data from LAS file (convenience function)
    
    Args:
        file_path: Path to LAS file
        curves: Specific curves to load
        
    Returns:
        dict: Processed curves dictionary
    """
    loader = DataLoader()
    return loader.load_las(file_path, curves)

def load_well_data(file_path: str, file_format: str = None, curves: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
    """
    Load well log data from file with automatic format detection
    
    Args:
        file_path: Path to data file
        file_format: Explicit format ('hdf5' or 'las')
        curves: Specific curves to load
        
    Returns:
        dict: Processed curves dictionary
    """
    if file_format == 'hdf5' or file_path.endswith('.h5') or file_path.endswith('.hdf5'):
        return load_hdf5(file_path, curves)
    elif file_format == 'las' or file_path.endswith('.las'):
        return load_las(file_path, curves)
    else:
        # Try HDF5 first, then LAS
        try:
            return load_hdf5(file_path, curves)
        except:
            return load_las(file_path, curves)