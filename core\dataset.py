"""
SingleTargetDataset for well log prediction
Implements windowing (720→640 with 50% overlap) for single-target training
"""

import torch
import numpy as np
from typing import Dict, <PERSON><PERSON>, Optional
from .data_loader import load_well_data
from .normalizer import create_curve_normalizer
from ..configs.templates import get_template

class SingleTargetDataset(torch.utils.data.Dataset):
    """
    Dataset for single-target well log prediction with windowing
    Supports VP and density prediction templates
    """
    
    def __init__(self, 
                 data_file: str,
                 template_name: str = 'vp_prediction',
                 total_length: int = 720,
                 effective_length: int = 640,
                 normalize: bool = True,
                 transform: bool = True,
                 device: Optional[torch.device] = None):
        """
        Initialize SingleTargetDataset
        
        Args:
            data_file: Path to HDF5 or LAS data file
            template_name: 'vp_prediction' or 'density_prediction'
            total_length: Total window length (720)
            effective_length: Effective sequence length after augmentation (640)
            normalize: Whether to apply normalization
            transform: Whether to apply random window augmentation
            device: PyTorch device
        """
        self.total_length = total_length
        self.effective_length = effective_length
        self.normalize = normalize
        self.transform = transform
        self.device = device or torch.device('cpu')
        
        # Load template configuration
        self.template = get_template(template_name)
        self.input_curves = self.template['input_curves']
        self.target_curve = self.template['output_curves'][0]
        
        # Load data
        self.curves_data = load_well_data(data_file, curves=self.input_curves + [self.target_curve])
        
        # Validate data
        self._validate_data()
        
        # Create normalizers
        if self.normalize:
            self.input_normalizers = {curve: create_curve_normalizer(curve) for curve in self.input_curves}
            self.target_normalizer = create_curve_normalizer(self.target_curve)
            
            # Fit normalizers to data
            self._fit_normalizers()
        else:
            self.input_normalizers = None
            self.target_normalizer = None
        
        # Create windows
        self.windows = self._create_windows()
        
        print(f"SingleTargetDataset initialized:")
        print(f"  Template: {template_name}")
        print(f"  Input curves: {self.input_curves}")
        print(f"  Target curve: {self.target_curve}")
        print(f"  Number of windows: {len(self.windows)}")
    
    def _validate_data(self):
        """Validate loaded data"""
        seq_len = len(list(self.curves_data.values())[0])
        
        # Check all curves have same length
        for curve_name, data in self.curves_data.items():
            if len(data) != seq_len:
                raise ValueError(f"Curve {curve_name} has length {len(data)}, expected {seq_len}")
        
        # Check required curves present
        missing_curves = [curve for curve in self.input_curves + [self.target_curve] 
                         if curve not in self.curves_data]
        if missing_curves:
            raise ValueError(f"Missing required curves: {missing_curves}")
        
        if seq_len < self.total_length:
            raise ValueError(f"Data length {seq_len} is less than required total_length {self.total_length}")
    
    def _fit_normalizers(self):
        """Fit normalizers to the data"""
        # Fit input normalizers
        for curve in self.input_curves:
            if curve in self.curves_data:
                self.input_normalizers[curve].fit(self.curves_data[curve])
        
        # Fit target normalizer
        if self.target_curve in self.curves_data:
            self.target_normalizer.fit(self.curves_data[self.target_curve])
    
    def _create_windows(self) -> List[Tuple]:
        """
        Create overlapping windows from the data
        
        Returns:
            List of (start_idx, input_data, target_data) tuples
        """
        seq_len = len(list(self.curves_data.values())[0])
        step_size = self.total_length // 2  # 50% overlap
        num_windows = 1 + (seq_len - self.total_length) // step_size
        
        windows = []
        for i in range(num_windows):
            start_idx = i * step_size
            
            # Extract input curves for this window
            input_window = {}
            for curve in self.input_curves:
                data = self.curves_data[curve]
                window_data = data[start_idx:start_idx + self.total_length]
                input_window[curve] = window_data
            
            # Extract target for this window
            target_data = self.curves_data[self.target_curve][start_idx:start_idx + self.total_length]
            
            windows.append((start_idx, input_window, target_data))
        
        return windows
    
    def __len__(self) -> int:
        """Return number of windows/samples"""
        return len(self.windows)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get a single training sample
        
        Args:
            idx: Window index
            
        Returns:
            Tuple of (input_tensor, target_tensor)
            - input_tensor: [num_input_curves, total_length]
            - target_tensor: [total_length]
        """
        if idx >= len(self):
            raise IndexError(f"Index {idx} out of range for dataset size {len(self)}")
        
        start_idx, input_window, target_data = self.windows[idx]
        
        # Apply transformation (random window start) if enabled
        if self.transform and self.total_length > self.effective_length:
            # Random start within the total window
            random_start = np.random.randint(0, self.total_length - self.effective_length + 1)
            window_start = start_idx + random_start
            window_end = window_start + self.effective_length
        else:
            window_start = start_idx
            window_end = start_idx + self.effective_length
        
        # Extract windowed data
        input_tensors = []
        for curve in self.input_curves:
            curve_data = self.curves_data[curve][window_start:window_end]
            if self.normalize:
                normalized_data = self.input_normalizers[curve].normalize(curve_data)
            else:
                normalized_data = torch.FloatTensor(curve_data)
            input_tensors.append(normalized_data)
        
        # Stack input curves [num_input_curves, effective_length]
        input_tensor = torch.stack(input_tensors, dim=0)
        input_tensor = input_tensor.to(self.device)
        
        # Normalize target
        if self.normalize:
            target_normalized = self.target_normalizer.normalize(target_data)
            target_tensor = torch.FloatTensor(target_normalized).unsqueeze(0).to(self.device)  # [1, effective_length]
        else:
            target_tensor = torch.FloatTensor(target_data).unsqueeze(0).to(self.device)
        
        return input_tensor, target_tensor

# Factory functions for common templates
def create_vp_dataset(data_file: str, normalize: bool = True, transform: bool = True):
    """Create dataset for VP prediction"""
    return SingleTargetDataset(
        data_file=data_file,
        template_name='vp_prediction',
        normalize=normalize,
        transform=transform
    )

def create_density_dataset(data_file: str, normalize: bool = True, transform: bool = True):
    """Create dataset for density prediction"""
    return SingleTargetDataset(
        data_file=data_file,
        template_name='density_prediction',
        normalize=normalize,
        transform=transform
    )