"""
CLI training pipeline for single-target well log prediction
Supports 'vp_prediction' and 'density_prediction' templates
"""

import argparse
import torch
import os
from pathlib import Path
from ..core.utils import get_device
from ..core.model import MWLT_Base
from ..core.dataset import SingleTargetDataset
from ..core.training import <PERSON><PERSON><PERSON><PERSON>, create_vp_trainer, create_density_trainer

def parse_arguments():
    parser = argparse.ArgumentParser(description='Train single-target well log model')
    parser.add_argument('--template', required=True, choices=['vp_prediction', 'density_prediction'],
                        help='Template for training (vp_prediction or density_prediction)')
    parser.add_argument('--train_file', required=True,
                        help='Path to training data file (HDF5 or LAS)')
    parser.add_argument('--val_file', required=True,
                        help='Path to validation data file (HDF5 or LAS)')
    parser.add_argument('--save_path', required=True,
                        help='Path to save training outputs')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='Initial learning rate')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size')
    parser.add_argument('--max_epochs', type=int, default=100,
                        help='Maximum number of epochs')
    parser.add_argument('--use_scheduler', action='store_true', default=True,
                        help='Use learning rate scheduler')
    parser.add_argument('--grad_clip', type=float, default=1.0,
                        help='Gradient clipping value')
    
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    print(f"Training configuration:")
    print(f"  Template: {args.template}")
    print(f"  Train file: {args.train_file}")
    print(f"  Val file: {args.val_file}")
    print(f"  Save path: {args.save_path}")
    
    # Device setup
    device = get_device()
    
    # Create datasets
    train_dataset = SingleTargetDataset(
        data_file=args.train_file,
        template_name=args.template,
        normalize=True,
        transform=True,
        device=device
    )
    
    val_dataset = SingleTargetDataset(
        data_file=args.val_file,
        template_name=args.template,
        normalize=True,
        transform=False,  # No augmentation for validation
        device=device
    )
    
    # Create model based on template
    # For both templates, use MWLT_Base with appropriate input channels
    input_curves = train_dataset.input_curves
    model = MWLT_Base(in_channels=len(input_curves), out_channels=1)
    
    # Create trainer
    target_curve = train_dataset.target_curve
    
    trainer = TrainingManager(
        model=model,
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        target_curve=target_curve,
        learning_rate=args.learning_rate,
        batch_size=args.batch_size,
        max_epochs=args.max_epochs,
        use_scheduler=args.use_scheduler,
        grad_clip=args.grad_clip,
        save_path=args.save_path
    )
    
    # Train the model
    print("Starting training...")
    training_results = trainer.train()
    
    print("Training completed!")
    print(f"Best validation loss: {training_results['best_val_loss']:.6f}")
    print(f"Training history saved to {args.save_path}/training_history.json")

if __name__ == "__main__":
    main()