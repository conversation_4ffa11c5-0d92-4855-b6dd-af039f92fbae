"""
General Well Log Predictor API
Adapted for init_gen_log_sono with template-based initialization
Supports single-target prediction using MWLT architecture
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from ..core.model import MWLT_Base
from ..core.checkpoint import load_checkpoint
from ..configs.curves import get_curve_definition
from ..configs.templates import get_template
from ..configs.validation import validate_full_configuration

class GeneralPredictor:
    """
    General-purpose well log prediction API for single-target prediction
    
    Supports template-based initialization for VP and density prediction
    using the MWLT architecture with unified data handling and normalization.
    """
    
    def __init__(self,
                 model_path: str = None,
                 template_name: str = None,
                 device_id: int = 0,
                 model: torch.nn.Module = None):
        """
        Initialize GeneralPredictor
        
        Args:
            model_path: Path to trained model checkpoint
            template_name: Template name ('vp_prediction' or 'density_prediction')
            device_id: GPU device ID (0 for first GPU, -1 for CPU)
            model: Pre-loaded model (optional)
        """
        # Device setup
        if device_id >= 0 and torch.cuda.is_available():
            self.device = torch.device(f"cuda:{device_id}")
        else:
            self.device = torch.device("cpu")
        print(f"Using device: {self.device}")
        
        self.model_path = model_path
        self.template_name = template_name
        
        # Load configuration from template
        if template_name:
            self.template = get_template(template_name)
            self.input_curves = self.template['input_curves']
            self.output_curves = self.template['output_curves']
            self.model_config = self.template['model_config']
            self.training_config = self.template.get('training_config', {})
        else:
            raise ValueError("template_name must be provided for initialization")
        
        # Validate configuration
        validation_results = validate_full_configuration(template_name=template_name)
        if not validation_results['valid']:
            print(f"Configuration warnings: {validation_results['warnings']}")
        
        # Create model
        if model is not None:
            self.model = model
        elif model_path is not None:
            self.model = self._load_model(model_path)
        else:
            # Create new model
            self.model = self._create_model()
        
        self.model.to(self.device)
        self.model.eval()
    
    @classmethod
    def from_template(cls, template_name: str, model_path: str = None, **kwargs):
        """
        Factory method to create predictor from template
        
        Args:
            template_name: 'vp_prediction' or 'density_prediction'
            model_path: Path to trained model checkpoint
            **kwargs: Additional arguments for __init__
            
        Returns:
            GeneralPredictor: Initialized predictor
        """
        return cls(template_name=template_name, model_path=model_path, **kwargs)
    
    def _create_model(self) -> torch.nn.Module:
        """Create MWLT model based on configuration"""
        # Use MWLT_Base for both templates as per minimalist v1
        model = MWLT_Base(in_channels=len(self.input_curves), out_channels=1)
        return model
    
    def _load_model(self, model_path: str) -> torch.nn.Module:
        """Load model from checkpoint"""
        model = self._create_model()
        model_dict, epoch, loss = load_checkpoint(model_path, self.device)
        
        try:
            model.load_state_dict(model_dict)
            print(f"Model loaded successfully. Epoch: {epoch}, Loss: {loss}")
        except Exception as e:
            print(f"Error loading model state_dict: {e}")
            raise
        
        return model
    
    def predict_from_curves(self, 
                           curves_data: Dict[str, Union[np.ndarray, torch.Tensor]]) -> Dict[str, np.ndarray]:
        """
        Predict from curve data dictionary (single-target)
        
        Args:
            curves_data: Dictionary mapping curve names to data arrays
            
        Returns:
            dict: Dictionary with predicted output curve
        """
        # Note: Full normalization and data loading will be implemented after
        # normalizer.py and data_loader.py are created. For now, placeholder
        
        # Validate input curves
        missing_curves = [curve for curve in self.input_curves if curve not in curves_data]
        if missing_curves:
            raise ValueError(f"Missing required input curves: {missing_curves}")
        
        # Prepare input tensor [B=1, C=num_input_curves, L=sequence_length]
        input_list = []
        seq_len = None
        
        for curve in self.input_curves:
            curve_data = curves_data[curve]
            if isinstance(curve_data, np.ndarray):
                curve_tensor = torch.from_numpy(curve_data).float()
            else:
                curve_tensor = curve_data.float()
            
            if seq_len is None:
                seq_len = curve_tensor.shape[-1]
            elif seq_len != curve_tensor.shape[-1]:
                raise ValueError(f"Curve {curve} has different length {curve_tensor.shape[-1]} than {seq_len}")
            
            input_list.append(curve_tensor.unsqueeze(0))  # Add batch dim
        
        # Stack inputs [1, C, L]
        input_tensor = torch.stack(input_list, dim=1).to(self.device)  # [1, C, L]
        
        with torch.no_grad():
            # Forward pass
            predictions = self.model(input_tensor)  # [1, 1, L]
            
            # Extract single output
            output_curve = self.output_curves[0]
            pred_array = predictions.squeeze(1).squeeze(0).cpu().numpy()  # [L]
            
            # Placeholder for denormalization - will integrate with normalizer.py later
            # For now, return raw predictions
            print(f"Warning: Predictions are raw model outputs. Normalization not applied yet.")
        
        return {output_curve: pred_array}
    
    def predict_from_file(self, file_path: str) -> Dict[str, np.ndarray]:
        """
        Predict from HDF5 or LAS file (placeholder - requires data_loader.py)
        
        Args:
            file_path: Path to input file
            
        Returns:
            dict: Predicted output curve
        """
        # Placeholder - will implement after data_loader.py is created
        raise NotImplementedError("File prediction requires data_loader.py implementation")
    
    def get_configuration(self) -> Dict:
        """
        Get current predictor configuration
        
        Returns:
            dict: Complete configuration including template and curves
        """
        return {
            'template_name': self.template_name,
            'template': self.template,
            'input_curves': self.input_curves,
            'output_curves': self.output_curves,
            'model_config': self.model_config,
            'device': str(self.device),
            'model_path': self.model_path
        }
    
    def get_model_info(self) -> Dict:
        """
        Get model information and parameters
        
        Returns:
            dict: Model details including parameter count
        """
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        return {
            'model_type': 'MWLT_Base',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'input_channels': len(self.input_curves),
            'output_channels': len(self.output_curves),
            'device': str(self.device)
        }